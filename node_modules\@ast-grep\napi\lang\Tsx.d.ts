// Auto-generated from tree-sitter Tsx v0.23.2
type TsxTypes = {
  "declaration": {
    "type": "declaration",
    "named": true,
    "subtypes": [
      {
        "type": "abstract_class_declaration",
        "named": true
      },
      {
        "type": "ambient_declaration",
        "named": true
      },
      {
        "type": "class_declaration",
        "named": true
      },
      {
        "type": "enum_declaration",
        "named": true
      },
      {
        "type": "function_declaration",
        "named": true
      },
      {
        "type": "function_signature",
        "named": true
      },
      {
        "type": "generator_function_declaration",
        "named": true
      },
      {
        "type": "import_alias",
        "named": true
      },
      {
        "type": "interface_declaration",
        "named": true
      },
      {
        "type": "internal_module",
        "named": true
      },
      {
        "type": "lexical_declaration",
        "named": true
      },
      {
        "type": "module",
        "named": true
      },
      {
        "type": "type_alias_declaration",
        "named": true
      },
      {
        "type": "variable_declaration",
        "named": true
      }
    ]
  },
  "expression": {
    "type": "expression",
    "named": true,
    "subtypes": [
      {
        "type": "as_expression",
        "named": true
      },
      {
        "type": "assignment_expression",
        "named": true
      },
      {
        "type": "augmented_assignment_expression",
        "named": true
      },
      {
        "type": "await_expression",
        "named": true
      },
      {
        "type": "binary_expression",
        "named": true
      },
      {
        "type": "instantiation_expression",
        "named": true
      },
      {
        "type": "internal_module",
        "named": true
      },
      {
        "type": "jsx_element",
        "named": true
      },
      {
        "type": "jsx_self_closing_element",
        "named": true
      },
      {
        "type": "new_expression",
        "named": true
      },
      {
        "type": "primary_expression",
        "named": true
      },
      {
        "type": "satisfies_expression",
        "named": true
      },
      {
        "type": "ternary_expression",
        "named": true
      },
      {
        "type": "unary_expression",
        "named": true
      },
      {
        "type": "update_expression",
        "named": true
      },
      {
        "type": "yield_expression",
        "named": true
      }
    ]
  },
  "pattern": {
    "type": "pattern",
    "named": true,
    "subtypes": [
      {
        "type": "array_pattern",
        "named": true
      },
      {
        "type": "identifier",
        "named": true
      },
      {
        "type": "member_expression",
        "named": true
      },
      {
        "type": "non_null_expression",
        "named": true
      },
      {
        "type": "object_pattern",
        "named": true
      },
      {
        "type": "rest_pattern",
        "named": true
      },
      {
        "type": "subscript_expression",
        "named": true
      },
      {
        "type": "undefined",
        "named": true
      }
    ]
  },
  "primary_expression": {
    "type": "primary_expression",
    "named": true,
    "subtypes": [
      {
        "type": "array",
        "named": true
      },
      {
        "type": "arrow_function",
        "named": true
      },
      {
        "type": "call_expression",
        "named": true
      },
      {
        "type": "class",
        "named": true
      },
      {
        "type": "false",
        "named": true
      },
      {
        "type": "function_expression",
        "named": true
      },
      {
        "type": "generator_function",
        "named": true
      },
      {
        "type": "identifier",
        "named": true
      },
      {
        "type": "member_expression",
        "named": true
      },
      {
        "type": "meta_property",
        "named": true
      },
      {
        "type": "non_null_expression",
        "named": true
      },
      {
        "type": "null",
        "named": true
      },
      {
        "type": "number",
        "named": true
      },
      {
        "type": "object",
        "named": true
      },
      {
        "type": "parenthesized_expression",
        "named": true
      },
      {
        "type": "regex",
        "named": true
      },
      {
        "type": "string",
        "named": true
      },
      {
        "type": "subscript_expression",
        "named": true
      },
      {
        "type": "super",
        "named": true
      },
      {
        "type": "template_string",
        "named": true
      },
      {
        "type": "this",
        "named": true
      },
      {
        "type": "true",
        "named": true
      },
      {
        "type": "undefined",
        "named": true
      }
    ]
  },
  "primary_type": {
    "type": "primary_type",
    "named": true,
    "subtypes": [
      {
        "type": "array_type",
        "named": true
      },
      {
        "type": "conditional_type",
        "named": true
      },
      {
        "type": "existential_type",
        "named": true
      },
      {
        "type": "flow_maybe_type",
        "named": true
      },
      {
        "type": "generic_type",
        "named": true
      },
      {
        "type": "index_type_query",
        "named": true
      },
      {
        "type": "intersection_type",
        "named": true
      },
      {
        "type": "literal_type",
        "named": true
      },
      {
        "type": "lookup_type",
        "named": true
      },
      {
        "type": "nested_type_identifier",
        "named": true
      },
      {
        "type": "object_type",
        "named": true
      },
      {
        "type": "parenthesized_type",
        "named": true
      },
      {
        "type": "predefined_type",
        "named": true
      },
      {
        "type": "template_literal_type",
        "named": true
      },
      {
        "type": "this_type",
        "named": true
      },
      {
        "type": "tuple_type",
        "named": true
      },
      {
        "type": "type_identifier",
        "named": true
      },
      {
        "type": "type_query",
        "named": true
      },
      {
        "type": "union_type",
        "named": true
      }
    ]
  },
  "statement": {
    "type": "statement",
    "named": true,
    "subtypes": [
      {
        "type": "break_statement",
        "named": true
      },
      {
        "type": "continue_statement",
        "named": true
      },
      {
        "type": "debugger_statement",
        "named": true
      },
      {
        "type": "declaration",
        "named": true
      },
      {
        "type": "do_statement",
        "named": true
      },
      {
        "type": "empty_statement",
        "named": true
      },
      {
        "type": "export_statement",
        "named": true
      },
      {
        "type": "expression_statement",
        "named": true
      },
      {
        "type": "for_in_statement",
        "named": true
      },
      {
        "type": "for_statement",
        "named": true
      },
      {
        "type": "if_statement",
        "named": true
      },
      {
        "type": "import_statement",
        "named": true
      },
      {
        "type": "labeled_statement",
        "named": true
      },
      {
        "type": "return_statement",
        "named": true
      },
      {
        "type": "statement_block",
        "named": true
      },
      {
        "type": "switch_statement",
        "named": true
      },
      {
        "type": "throw_statement",
        "named": true
      },
      {
        "type": "try_statement",
        "named": true
      },
      {
        "type": "while_statement",
        "named": true
      },
      {
        "type": "with_statement",
        "named": true
      }
    ]
  },
  "type": {
    "type": "type",
    "named": true,
    "subtypes": [
      {
        "type": "call_expression",
        "named": true
      },
      {
        "type": "constructor_type",
        "named": true
      },
      {
        "type": "function_type",
        "named": true
      },
      {
        "type": "infer_type",
        "named": true
      },
      {
        "type": "member_expression",
        "named": true
      },
      {
        "type": "primary_type",
        "named": true
      },
      {
        "type": "readonly_type",
        "named": true
      }
    ]
  },
  "abstract_class_declaration": {
    "type": "abstract_class_declaration",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "class_body",
            "named": true
          }
        ]
      },
      "decorator": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "decorator",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "class_heritage",
          "named": true
        }
      ]
    }
  },
  "abstract_method_signature": {
    "type": "abstract_method_signature",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "computed_property_name",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "accessibility_modifier",
          "named": true
        },
        {
          "type": "override_modifier",
          "named": true
        }
      ]
    }
  },
  "accessibility_modifier": {
    "type": "accessibility_modifier",
    "named": true,
    "fields": {}
  },
  "adding_type_annotation": {
    "type": "adding_type_annotation",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "ambient_declaration": {
    "type": "ambient_declaration",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "declaration",
          "named": true
        },
        {
          "type": "property_identifier",
          "named": true
        },
        {
          "type": "statement_block",
          "named": true
        },
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "arguments": {
    "type": "arguments",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "spread_element",
          "named": true
        }
      ]
    }
  },
  "array": {
    "type": "array",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "spread_element",
          "named": true
        }
      ]
    }
  },
  "array_pattern": {
    "type": "array_pattern",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "assignment_pattern",
          "named": true
        },
        {
          "type": "pattern",
          "named": true
        }
      ]
    }
  },
  "array_type": {
    "type": "array_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "primary_type",
          "named": true
        }
      ]
    }
  },
  "arrow_function": {
    "type": "arrow_function",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "parameter": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      },
      "parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "as_expression": {
    "type": "as_expression",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "asserts": {
    "type": "asserts",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "identifier",
          "named": true
        },
        {
          "type": "this",
          "named": true
        },
        {
          "type": "type_predicate",
          "named": true
        }
      ]
    }
  },
  "asserts_annotation": {
    "type": "asserts_annotation",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "asserts",
          "named": true
        }
      ]
    }
  },
  "assignment_expression": {
    "type": "assignment_expression",
    "named": true,
    "fields": {
      "left": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "array_pattern",
            "named": true
          },
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "member_expression",
            "named": true
          },
          {
            "type": "non_null_expression",
            "named": true
          },
          {
            "type": "object_pattern",
            "named": true
          },
          {
            "type": "parenthesized_expression",
            "named": true
          },
          {
            "type": "subscript_expression",
            "named": true
          },
          {
            "type": "undefined",
            "named": true
          }
        ]
      },
      "right": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "assignment_pattern": {
    "type": "assignment_pattern",
    "named": true,
    "fields": {
      "left": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "pattern",
            "named": true
          }
        ]
      },
      "right": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "augmented_assignment_expression": {
    "type": "augmented_assignment_expression",
    "named": true,
    "fields": {
      "left": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "member_expression",
            "named": true
          },
          {
            "type": "non_null_expression",
            "named": true
          },
          {
            "type": "parenthesized_expression",
            "named": true
          },
          {
            "type": "subscript_expression",
            "named": true
          }
        ]
      },
      "operator": {
        "multiple": false,
        "required": true,
        "types": []
      },
      "right": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "await_expression": {
    "type": "await_expression",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        }
      ]
    }
  },
  "binary_expression": {
    "type": "binary_expression",
    "named": true,
    "fields": {
      "left": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          }
        ]
      },
      "operator": {
        "multiple": false,
        "required": true,
        "types": []
      },
      "right": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "break_statement": {
    "type": "break_statement",
    "named": true,
    "fields": {
      "label": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "statement_identifier",
            "named": true
          }
        ]
      }
    }
  },
  "call_expression": {
    "type": "call_expression",
    "named": true,
    "fields": {
      "arguments": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "arguments",
            "named": true
          },
          {
            "type": "template_string",
            "named": true
          }
        ]
      },
      "function": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "import",
            "named": true
          }
        ]
      },
      "type_arguments": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_arguments",
            "named": true
          }
        ]
      }
    }
  },
  "call_signature": {
    "type": "call_signature",
    "named": true,
    "fields": {
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "catch_clause": {
    "type": "catch_clause",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "parameter": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "array_pattern",
            "named": true
          },
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "object_pattern",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_annotation",
            "named": true
          }
        ]
      }
    }
  },
  "class": {
    "type": "class",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "class_body",
            "named": true
          }
        ]
      },
      "decorator": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "decorator",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "class_heritage",
          "named": true
        }
      ]
    }
  },
  "class_body": {
    "type": "class_body",
    "named": true,
    "fields": {
      "decorator": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "decorator",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "abstract_method_signature",
          "named": true
        },
        {
          "type": "class_static_block",
          "named": true
        },
        {
          "type": "index_signature",
          "named": true
        },
        {
          "type": "method_definition",
          "named": true
        },
        {
          "type": "method_signature",
          "named": true
        },
        {
          "type": "public_field_definition",
          "named": true
        }
      ]
    }
  },
  "class_declaration": {
    "type": "class_declaration",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "class_body",
            "named": true
          }
        ]
      },
      "decorator": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "decorator",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "class_heritage",
          "named": true
        }
      ]
    }
  },
  "class_heritage": {
    "type": "class_heritage",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "extends_clause",
          "named": true
        },
        {
          "type": "implements_clause",
          "named": true
        }
      ]
    }
  },
  "class_static_block": {
    "type": "class_static_block",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      }
    }
  },
  "computed_property_name": {
    "type": "computed_property_name",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        }
      ]
    }
  },
  "conditional_type": {
    "type": "conditional_type",
    "named": true,
    "fields": {
      "alternative": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      },
      "consequence": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      },
      "left": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      },
      "right": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      }
    }
  },
  "constraint": {
    "type": "constraint",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "construct_signature": {
    "type": "construct_signature",
    "named": true,
    "fields": {
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "constructor_type": {
    "type": "constructor_type",
    "named": true,
    "fields": {
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "continue_statement": {
    "type": "continue_statement",
    "named": true,
    "fields": {
      "label": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "statement_identifier",
            "named": true
          }
        ]
      }
    }
  },
  "debugger_statement": {
    "type": "debugger_statement",
    "named": true,
    "fields": {}
  },
  "decorator": {
    "type": "decorator",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "identifier",
          "named": true
        },
        {
          "type": "member_expression",
          "named": true
        },
        {
          "type": "parenthesized_expression",
          "named": true
        }
      ]
    }
  },
  "default_type": {
    "type": "default_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "do_statement": {
    "type": "do_statement",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement",
            "named": true
          }
        ]
      },
      "condition": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "parenthesized_expression",
            "named": true
          }
        ]
      }
    }
  },
  "else_clause": {
    "type": "else_clause",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "statement",
          "named": true
        }
      ]
    }
  },
  "empty_statement": {
    "type": "empty_statement",
    "named": true,
    "fields": {}
  },
  "enum_assignment": {
    "type": "enum_assignment",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "computed_property_name",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "enum_body": {
    "type": "enum_body",
    "named": true,
    "fields": {
      "name": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "computed_property_name",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "enum_assignment",
          "named": true
        }
      ]
    }
  },
  "enum_declaration": {
    "type": "enum_declaration",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "enum_body",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      }
    }
  },
  "existential_type": {
    "type": "existential_type",
    "named": true,
    "fields": {}
  },
  "export_clause": {
    "type": "export_clause",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "export_specifier",
          "named": true
        }
      ]
    }
  },
  "export_specifier": {
    "type": "export_specifier",
    "named": true,
    "fields": {
      "alias": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      }
    }
  },
  "export_statement": {
    "type": "export_statement",
    "named": true,
    "fields": {
      "declaration": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "declaration",
            "named": true
          }
        ]
      },
      "decorator": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "decorator",
            "named": true
          }
        ]
      },
      "source": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "export_clause",
          "named": true
        },
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "identifier",
          "named": true
        },
        {
          "type": "namespace_export",
          "named": true
        }
      ]
    }
  },
  "expression_statement": {
    "type": "expression_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "sequence_expression",
          "named": true
        }
      ]
    }
  },
  "extends_clause": {
    "type": "extends_clause",
    "named": true,
    "fields": {
      "type_arguments": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "type_arguments",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": true,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "extends_type_clause": {
    "type": "extends_type_clause",
    "named": true,
    "fields": {
      "type": {
        "multiple": true,
        "required": true,
        "types": [
          {
            "type": "generic_type",
            "named": true
          },
          {
            "type": "nested_type_identifier",
            "named": true
          },
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      }
    }
  },
  "finally_clause": {
    "type": "finally_clause",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      }
    }
  },
  "flow_maybe_type": {
    "type": "flow_maybe_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "primary_type",
          "named": true
        }
      ]
    }
  },
  "for_in_statement": {
    "type": "for_in_statement",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement",
            "named": true
          }
        ]
      },
      "kind": {
        "multiple": false,
        "required": false,
        "types": []
      },
      "left": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "array_pattern",
            "named": true
          },
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "member_expression",
            "named": true
          },
          {
            "type": "non_null_expression",
            "named": true
          },
          {
            "type": "object_pattern",
            "named": true
          },
          {
            "type": "parenthesized_expression",
            "named": true
          },
          {
            "type": "subscript_expression",
            "named": true
          },
          {
            "type": "undefined",
            "named": true
          }
        ]
      },
      "operator": {
        "multiple": false,
        "required": true,
        "types": []
      },
      "right": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "sequence_expression",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "for_statement": {
    "type": "for_statement",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement",
            "named": true
          }
        ]
      },
      "condition": {
        "multiple": true,
        "required": true,
        "types": [
          {
            "type": "empty_statement",
            "named": true
          },
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "sequence_expression",
            "named": true
          }
        ]
      },
      "increment": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "sequence_expression",
            "named": true
          }
        ]
      },
      "initializer": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "empty_statement",
            "named": true
          },
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "lexical_declaration",
            "named": true
          },
          {
            "type": "sequence_expression",
            "named": true
          },
          {
            "type": "variable_declaration",
            "named": true
          }
        ]
      }
    }
  },
  "formal_parameters": {
    "type": "formal_parameters",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "optional_parameter",
          "named": true
        },
        {
          "type": "required_parameter",
          "named": true
        }
      ]
    }
  },
  "function_declaration": {
    "type": "function_declaration",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      },
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "function_expression": {
    "type": "function_expression",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      },
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "function_signature": {
    "type": "function_signature",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      },
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "function_type": {
    "type": "function_type",
    "named": true,
    "fields": {
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "asserts",
            "named": true
          },
          {
            "type": "type",
            "named": true
          },
          {
            "type": "type_predicate",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "generator_function": {
    "type": "generator_function",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      },
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "generator_function_declaration": {
    "type": "generator_function_declaration",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      },
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    }
  },
  "generic_type": {
    "type": "generic_type",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "nested_type_identifier",
            "named": true
          },
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      },
      "type_arguments": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type_arguments",
            "named": true
          }
        ]
      }
    }
  },
  "identifier": {
    "type": "identifier",
    "named": true,
    "fields": {}
  },
  "if_statement": {
    "type": "if_statement",
    "named": true,
    "fields": {
      "alternative": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "else_clause",
            "named": true
          }
        ]
      },
      "condition": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "parenthesized_expression",
            "named": true
          }
        ]
      },
      "consequence": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement",
            "named": true
          }
        ]
      }
    }
  },
  "implements_clause": {
    "type": "implements_clause",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "import": {
    "type": "import",
    "named": true,
    "fields": {}
  },
  "import_alias": {
    "type": "import_alias",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "identifier",
          "named": true
        },
        {
          "type": "nested_identifier",
          "named": true
        }
      ]
    }
  },
  "import_attribute": {
    "type": "import_attribute",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "object",
          "named": true
        }
      ]
    }
  },
  "import_clause": {
    "type": "import_clause",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "identifier",
          "named": true
        },
        {
          "type": "named_imports",
          "named": true
        },
        {
          "type": "namespace_import",
          "named": true
        }
      ]
    }
  },
  "import_require_clause": {
    "type": "import_require_clause",
    "named": true,
    "fields": {
      "source": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "string",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "identifier",
          "named": true
        }
      ]
    }
  },
  "import_specifier": {
    "type": "import_specifier",
    "named": true,
    "fields": {
      "alias": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      }
    }
  },
  "import_statement": {
    "type": "import_statement",
    "named": true,
    "fields": {
      "source": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "string",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "import_attribute",
          "named": true
        },
        {
          "type": "import_clause",
          "named": true
        },
        {
          "type": "import_require_clause",
          "named": true
        }
      ]
    }
  },
  "index_signature": {
    "type": "index_signature",
    "named": true,
    "fields": {
      "index_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      },
      "sign": {
        "multiple": false,
        "required": false,
        "types": []
      },
      "type": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "adding_type_annotation",
            "named": true
          },
          {
            "type": "omitting_type_annotation",
            "named": true
          },
          {
            "type": "opting_type_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "mapped_type_clause",
          "named": true
        }
      ]
    }
  },
  "index_type_query": {
    "type": "index_type_query",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "primary_type",
          "named": true
        }
      ]
    }
  },
  "infer_type": {
    "type": "infer_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        },
        {
          "type": "type_identifier",
          "named": true
        }
      ]
    }
  },
  "instantiation_expression": {
    "type": "instantiation_expression",
    "named": true,
    "fields": {
      "function": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "import",
            "named": true
          },
          {
            "type": "member_expression",
            "named": true
          },
          {
            "type": "subscript_expression",
            "named": true
          }
        ]
      },
      "type_arguments": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type_arguments",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "expression",
          "named": true
        }
      ]
    }
  },
  "interface_body": {
    "type": "interface_body",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "call_signature",
          "named": true
        },
        {
          "type": "construct_signature",
          "named": true
        },
        {
          "type": "export_statement",
          "named": true
        },
        {
          "type": "index_signature",
          "named": true
        },
        {
          "type": "method_signature",
          "named": true
        },
        {
          "type": "property_signature",
          "named": true
        }
      ]
    }
  },
  "interface_declaration": {
    "type": "interface_declaration",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "interface_body",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "extends_type_clause",
          "named": true
        }
      ]
    }
  },
  "internal_module": {
    "type": "internal_module",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "nested_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      }
    }
  },
  "intersection_type": {
    "type": "intersection_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "jsx_attribute": {
    "type": "jsx_attribute",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "jsx_element",
          "named": true
        },
        {
          "type": "jsx_expression",
          "named": true
        },
        {
          "type": "jsx_namespace_name",
          "named": true
        },
        {
          "type": "jsx_self_closing_element",
          "named": true
        },
        {
          "type": "property_identifier",
          "named": true
        },
        {
          "type": "string",
          "named": true
        }
      ]
    }
  },
  "jsx_closing_element": {
    "type": "jsx_closing_element",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "jsx_namespace_name",
            "named": true
          },
          {
            "type": "member_expression",
            "named": true
          }
        ]
      }
    }
  },
  "jsx_element": {
    "type": "jsx_element",
    "named": true,
    "fields": {
      "close_tag": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "jsx_closing_element",
            "named": true
          }
        ]
      },
      "open_tag": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "jsx_opening_element",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "html_character_reference",
          "named": true
        },
        {
          "type": "jsx_element",
          "named": true
        },
        {
          "type": "jsx_expression",
          "named": true
        },
        {
          "type": "jsx_self_closing_element",
          "named": true
        },
        {
          "type": "jsx_text",
          "named": true
        }
      ]
    }
  },
  "jsx_expression": {
    "type": "jsx_expression",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "sequence_expression",
          "named": true
        },
        {
          "type": "spread_element",
          "named": true
        }
      ]
    }
  },
  "jsx_namespace_name": {
    "type": "jsx_namespace_name",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "identifier",
          "named": true
        }
      ]
    }
  },
  "jsx_opening_element": {
    "type": "jsx_opening_element",
    "named": true,
    "fields": {
      "attribute": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "jsx_attribute",
            "named": true
          },
          {
            "type": "jsx_expression",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "jsx_namespace_name",
            "named": true
          },
          {
            "type": "member_expression",
            "named": true
          }
        ]
      },
      "type_arguments": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_arguments",
            "named": true
          }
        ]
      }
    }
  },
  "jsx_self_closing_element": {
    "type": "jsx_self_closing_element",
    "named": true,
    "fields": {
      "attribute": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "jsx_attribute",
            "named": true
          },
          {
            "type": "jsx_expression",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "jsx_namespace_name",
            "named": true
          },
          {
            "type": "member_expression",
            "named": true
          }
        ]
      },
      "type_arguments": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_arguments",
            "named": true
          }
        ]
      }
    }
  },
  "labeled_statement": {
    "type": "labeled_statement",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement",
            "named": true
          }
        ]
      },
      "label": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_identifier",
            "named": true
          }
        ]
      }
    }
  },
  "lexical_declaration": {
    "type": "lexical_declaration",
    "named": true,
    "fields": {
      "kind": {
        "multiple": false,
        "required": true,
        "types": []
      }
    },
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "variable_declarator",
          "named": true
        }
      ]
    }
  },
  "literal_type": {
    "type": "literal_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "false",
          "named": true
        },
        {
          "type": "null",
          "named": true
        },
        {
          "type": "number",
          "named": true
        },
        {
          "type": "string",
          "named": true
        },
        {
          "type": "true",
          "named": true
        },
        {
          "type": "unary_expression",
          "named": true
        },
        {
          "type": "undefined",
          "named": true
        }
      ]
    }
  },
  "lookup_type": {
    "type": "lookup_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "mapped_type_clause": {
    "type": "mapped_type_clause",
    "named": true,
    "fields": {
      "alias": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      }
    }
  },
  "member_expression": {
    "type": "member_expression",
    "named": true,
    "fields": {
      "object": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "import",
            "named": true
          }
        ]
      },
      "optional_chain": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "optional_chain",
            "named": true
          }
        ]
      },
      "property": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          }
        ]
      }
    }
  },
  "meta_property": {
    "type": "meta_property",
    "named": true,
    "fields": {}
  },
  "method_definition": {
    "type": "method_definition",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "computed_property_name",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "accessibility_modifier",
          "named": true
        },
        {
          "type": "override_modifier",
          "named": true
        }
      ]
    }
  },
  "method_signature": {
    "type": "method_signature",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "computed_property_name",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "parameters": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "formal_parameters",
            "named": true
          }
        ]
      },
      "return_type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "asserts_annotation",
            "named": true
          },
          {
            "type": "type_annotation",
            "named": true
          },
          {
            "type": "type_predicate_annotation",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "accessibility_modifier",
          "named": true
        },
        {
          "type": "override_modifier",
          "named": true
        }
      ]
    }
  },
  "module": {
    "type": "module",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "nested_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      }
    }
  },
  "named_imports": {
    "type": "named_imports",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "import_specifier",
          "named": true
        }
      ]
    }
  },
  "namespace_export": {
    "type": "namespace_export",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "identifier",
          "named": true
        },
        {
          "type": "string",
          "named": true
        }
      ]
    }
  },
  "namespace_import": {
    "type": "namespace_import",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "identifier",
          "named": true
        }
      ]
    }
  },
  "nested_identifier": {
    "type": "nested_identifier",
    "named": true,
    "fields": {
      "object": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "member_expression",
            "named": true
          }
        ]
      },
      "property": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "property_identifier",
            "named": true
          }
        ]
      }
    }
  },
  "nested_type_identifier": {
    "type": "nested_type_identifier",
    "named": true,
    "fields": {
      "module": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "nested_identifier",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      }
    }
  },
  "new_expression": {
    "type": "new_expression",
    "named": true,
    "fields": {
      "arguments": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "arguments",
            "named": true
          }
        ]
      },
      "constructor": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "primary_expression",
            "named": true
          }
        ]
      },
      "type_arguments": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_arguments",
            "named": true
          }
        ]
      }
    }
  },
  "non_null_expression": {
    "type": "non_null_expression",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        }
      ]
    }
  },
  "object": {
    "type": "object",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "method_definition",
          "named": true
        },
        {
          "type": "pair",
          "named": true
        },
        {
          "type": "shorthand_property_identifier",
          "named": true
        },
        {
          "type": "spread_element",
          "named": true
        }
      ]
    }
  },
  "object_assignment_pattern": {
    "type": "object_assignment_pattern",
    "named": true,
    "fields": {
      "left": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "array_pattern",
            "named": true
          },
          {
            "type": "object_pattern",
            "named": true
          },
          {
            "type": "shorthand_property_identifier_pattern",
            "named": true
          }
        ]
      },
      "right": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "object_pattern": {
    "type": "object_pattern",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "object_assignment_pattern",
          "named": true
        },
        {
          "type": "pair_pattern",
          "named": true
        },
        {
          "type": "rest_pattern",
          "named": true
        },
        {
          "type": "shorthand_property_identifier_pattern",
          "named": true
        }
      ]
    }
  },
  "object_type": {
    "type": "object_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "call_signature",
          "named": true
        },
        {
          "type": "construct_signature",
          "named": true
        },
        {
          "type": "export_statement",
          "named": true
        },
        {
          "type": "index_signature",
          "named": true
        },
        {
          "type": "method_signature",
          "named": true
        },
        {
          "type": "property_signature",
          "named": true
        }
      ]
    }
  },
  "omitting_type_annotation": {
    "type": "omitting_type_annotation",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "opting_type_annotation": {
    "type": "opting_type_annotation",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "optional_chain": {
    "type": "optional_chain",
    "named": true,
    "fields": {}
  },
  "optional_parameter": {
    "type": "optional_parameter",
    "named": true,
    "fields": {
      "decorator": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "decorator",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          }
        ]
      },
      "pattern": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "pattern",
            "named": true
          },
          {
            "type": "this",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_annotation",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "accessibility_modifier",
          "named": true
        },
        {
          "type": "override_modifier",
          "named": true
        }
      ]
    }
  },
  "optional_type": {
    "type": "optional_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "override_modifier": {
    "type": "override_modifier",
    "named": true,
    "fields": {}
  },
  "pair": {
    "type": "pair",
    "named": true,
    "fields": {
      "key": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "computed_property_name",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "pair_pattern": {
    "type": "pair_pattern",
    "named": true,
    "fields": {
      "key": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "computed_property_name",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "assignment_pattern",
            "named": true
          },
          {
            "type": "pattern",
            "named": true
          }
        ]
      }
    }
  },
  "parenthesized_expression": {
    "type": "parenthesized_expression",
    "named": true,
    "fields": {
      "type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_annotation",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "identifier",
          "named": true
        },
        {
          "type": "member_expression",
          "named": true
        },
        {
          "type": "sequence_expression",
          "named": true
        }
      ]
    }
  },
  "parenthesized_type": {
    "type": "parenthesized_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "predefined_type": {
    "type": "predefined_type",
    "named": true,
    "fields": {}
  },
  "program": {
    "type": "program",
    "named": true,
    "root": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "hash_bang_line",
          "named": true
        },
        {
          "type": "statement",
          "named": true
        }
      ]
    }
  },
  "property_signature": {
    "type": "property_signature",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "computed_property_name",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_annotation",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "accessibility_modifier",
          "named": true
        },
        {
          "type": "override_modifier",
          "named": true
        }
      ]
    }
  },
  "public_field_definition": {
    "type": "public_field_definition",
    "named": true,
    "fields": {
      "decorator": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "decorator",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "computed_property_name",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "private_property_identifier",
            "named": true
          },
          {
            "type": "property_identifier",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_annotation",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "accessibility_modifier",
          "named": true
        },
        {
          "type": "override_modifier",
          "named": true
        }
      ]
    }
  },
  "readonly_type": {
    "type": "readonly_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "regex": {
    "type": "regex",
    "named": true,
    "fields": {
      "flags": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "regex_flags",
            "named": true
          }
        ]
      },
      "pattern": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "regex_pattern",
            "named": true
          }
        ]
      }
    }
  },
  "required_parameter": {
    "type": "required_parameter",
    "named": true,
    "fields": {
      "decorator": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "decorator",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "rest_pattern",
            "named": true
          }
        ]
      },
      "pattern": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "pattern",
            "named": true
          },
          {
            "type": "this",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_annotation",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    },
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "accessibility_modifier",
          "named": true
        },
        {
          "type": "override_modifier",
          "named": true
        }
      ]
    }
  },
  "rest_pattern": {
    "type": "rest_pattern",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "array_pattern",
          "named": true
        },
        {
          "type": "identifier",
          "named": true
        },
        {
          "type": "member_expression",
          "named": true
        },
        {
          "type": "non_null_expression",
          "named": true
        },
        {
          "type": "object_pattern",
          "named": true
        },
        {
          "type": "subscript_expression",
          "named": true
        },
        {
          "type": "undefined",
          "named": true
        }
      ]
    }
  },
  "rest_type": {
    "type": "rest_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "return_statement": {
    "type": "return_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "sequence_expression",
          "named": true
        }
      ]
    }
  },
  "satisfies_expression": {
    "type": "satisfies_expression",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "sequence_expression": {
    "type": "sequence_expression",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        }
      ]
    }
  },
  "spread_element": {
    "type": "spread_element",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        }
      ]
    }
  },
  "statement_block": {
    "type": "statement_block",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "statement",
          "named": true
        }
      ]
    }
  },
  "string": {
    "type": "string",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "escape_sequence",
          "named": true
        },
        {
          "type": "html_character_reference",
          "named": true
        },
        {
          "type": "string_fragment",
          "named": true
        }
      ]
    }
  },
  "subscript_expression": {
    "type": "subscript_expression",
    "named": true,
    "fields": {
      "index": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "number",
            "named": true
          },
          {
            "type": "predefined_type",
            "named": true
          },
          {
            "type": "sequence_expression",
            "named": true
          },
          {
            "type": "string",
            "named": true
          }
        ]
      },
      "object": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      },
      "optional_chain": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "optional_chain",
            "named": true
          }
        ]
      }
    }
  },
  "switch_body": {
    "type": "switch_body",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "switch_case",
          "named": true
        },
        {
          "type": "switch_default",
          "named": true
        }
      ]
    }
  },
  "switch_case": {
    "type": "switch_case",
    "named": true,
    "fields": {
      "body": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "statement",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "sequence_expression",
            "named": true
          }
        ]
      }
    }
  },
  "switch_default": {
    "type": "switch_default",
    "named": true,
    "fields": {
      "body": {
        "multiple": true,
        "required": false,
        "types": [
          {
            "type": "statement",
            "named": true
          }
        ]
      }
    }
  },
  "switch_statement": {
    "type": "switch_statement",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "switch_body",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "parenthesized_expression",
            "named": true
          }
        ]
      }
    }
  },
  "template_literal_type": {
    "type": "template_literal_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "string_fragment",
          "named": true
        },
        {
          "type": "template_type",
          "named": true
        }
      ]
    }
  },
  "template_string": {
    "type": "template_string",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "escape_sequence",
          "named": true
        },
        {
          "type": "string_fragment",
          "named": true
        },
        {
          "type": "template_substitution",
          "named": true
        }
      ]
    }
  },
  "template_substitution": {
    "type": "template_substitution",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "sequence_expression",
          "named": true
        }
      ]
    }
  },
  "template_type": {
    "type": "template_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "infer_type",
          "named": true
        },
        {
          "type": "primary_type",
          "named": true
        }
      ]
    }
  },
  "ternary_expression": {
    "type": "ternary_expression",
    "named": true,
    "fields": {
      "alternative": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      },
      "condition": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      },
      "consequence": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "throw_statement": {
    "type": "throw_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "expression",
          "named": true
        },
        {
          "type": "sequence_expression",
          "named": true
        }
      ]
    }
  },
  "try_statement": {
    "type": "try_statement",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement_block",
            "named": true
          }
        ]
      },
      "finalizer": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "finally_clause",
            "named": true
          }
        ]
      },
      "handler": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "catch_clause",
            "named": true
          }
        ]
      }
    }
  },
  "tuple_type": {
    "type": "tuple_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "optional_parameter",
          "named": true
        },
        {
          "type": "optional_type",
          "named": true
        },
        {
          "type": "required_parameter",
          "named": true
        },
        {
          "type": "rest_type",
          "named": true
        },
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "type_alias_declaration": {
    "type": "type_alias_declaration",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      },
      "type_parameters": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_parameters",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      }
    }
  },
  "type_annotation": {
    "type": "type_annotation",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "type_arguments": {
    "type": "type_arguments",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "type_parameter": {
    "type": "type_parameter",
    "named": true,
    "fields": {
      "constraint": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "constraint",
            "named": true
          }
        ]
      },
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type_identifier",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "default_type",
            "named": true
          }
        ]
      }
    }
  },
  "type_parameters": {
    "type": "type_parameters",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "type_parameter",
          "named": true
        }
      ]
    }
  },
  "type_predicate": {
    "type": "type_predicate",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "this",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "type",
            "named": true
          }
        ]
      }
    }
  },
  "type_predicate_annotation": {
    "type": "type_predicate_annotation",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "type_predicate",
          "named": true
        }
      ]
    }
  },
  "type_query": {
    "type": "type_query",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "identifier",
          "named": true
        },
        {
          "type": "instantiation_expression",
          "named": true
        },
        {
          "type": "member_expression",
          "named": true
        },
        {
          "type": "subscript_expression",
          "named": true
        },
        {
          "type": "this",
          "named": true
        }
      ]
    }
  },
  "unary_expression": {
    "type": "unary_expression",
    "named": true,
    "fields": {
      "argument": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          },
          {
            "type": "number",
            "named": true
          }
        ]
      },
      "operator": {
        "multiple": false,
        "required": true,
        "types": []
      }
    }
  },
  "union_type": {
    "type": "union_type",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "type",
          "named": true
        }
      ]
    }
  },
  "update_expression": {
    "type": "update_expression",
    "named": true,
    "fields": {
      "argument": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      },
      "operator": {
        "multiple": false,
        "required": true,
        "types": []
      }
    }
  },
  "variable_declaration": {
    "type": "variable_declaration",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "variable_declarator",
          "named": true
        }
      ]
    }
  },
  "variable_declarator": {
    "type": "variable_declarator",
    "named": true,
    "fields": {
      "name": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "array_pattern",
            "named": true
          },
          {
            "type": "identifier",
            "named": true
          },
          {
            "type": "object_pattern",
            "named": true
          }
        ]
      },
      "type": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "type_annotation",
            "named": true
          }
        ]
      },
      "value": {
        "multiple": false,
        "required": false,
        "types": [
          {
            "type": "expression",
            "named": true
          }
        ]
      }
    }
  },
  "while_statement": {
    "type": "while_statement",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement",
            "named": true
          }
        ]
      },
      "condition": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "parenthesized_expression",
            "named": true
          }
        ]
      }
    }
  },
  "with_statement": {
    "type": "with_statement",
    "named": true,
    "fields": {
      "body": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "statement",
            "named": true
          }
        ]
      },
      "object": {
        "multiple": false,
        "required": true,
        "types": [
          {
            "type": "parenthesized_expression",
            "named": true
          }
        ]
      }
    }
  },
  "yield_expression": {
    "type": "yield_expression",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "expression",
          "named": true
        }
      ]
    }
  },
  "comment": {
    "type": "comment",
    "named": true
  },
  "escape_sequence": {
    "type": "escape_sequence",
    "named": true
  },
  "false": {
    "type": "false",
    "named": true
  },
  "hash_bang_line": {
    "type": "hash_bang_line",
    "named": true
  },
  "html_character_reference": {
    "type": "html_character_reference",
    "named": true
  },
  "html_comment": {
    "type": "html_comment",
    "named": true
  },
  "jsx_text": {
    "type": "jsx_text",
    "named": true
  },
  "null": {
    "type": "null",
    "named": true
  },
  "number": {
    "type": "number",
    "named": true
  },
  "private_property_identifier": {
    "type": "private_property_identifier",
    "named": true
  },
  "property_identifier": {
    "type": "property_identifier",
    "named": true
  },
  "regex_flags": {
    "type": "regex_flags",
    "named": true
  },
  "regex_pattern": {
    "type": "regex_pattern",
    "named": true
  },
  "shorthand_property_identifier": {
    "type": "shorthand_property_identifier",
    "named": true
  },
  "shorthand_property_identifier_pattern": {
    "type": "shorthand_property_identifier_pattern",
    "named": true
  },
  "statement_identifier": {
    "type": "statement_identifier",
    "named": true
  },
  "string_fragment": {
    "type": "string_fragment",
    "named": true
  },
  "super": {
    "type": "super",
    "named": true
  },
  "this": {
    "type": "this",
    "named": true
  },
  "this_type": {
    "type": "this_type",
    "named": true
  },
  "true": {
    "type": "true",
    "named": true
  },
  "type_identifier": {
    "type": "type_identifier",
    "named": true
  },
  "undefined": {
    "type": "undefined",
    "named": true
  }
};
export default TsxTypes;