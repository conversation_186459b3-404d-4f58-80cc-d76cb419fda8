{"version": 3, "file": "RunAction.js", "sourceRoot": "", "sources": ["../../src/cli/RunAction.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,uCAAyB;AACzB,2CAA6B;AAC7B,oEAMsC;AACtC,kDAA+C;AAC/C,gEAIoC;AAEpC,gDAAmE;AAEnE,4DAA8F;AAE9F,MAAa,SAAU,SAAQ,mCAAiB;IAO9C,YAAmB,MAA+B;QAChD,KAAK,CAAC;YACJ,UAAU,EAAE,KAAK;YACjB,OAAO,EAAE,mCAAmC;YAC5C,aAAa,EAAE,mCAAmC;SACnD,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACrD,iBAAiB,EAAE,UAAU;YAC7B,kBAAkB,EAAE,IAAI;YACxB,YAAY,EAAE,MAAM;YACpB,WAAW,EAAE,qBAAqB,iCAAe,CAAC,QAAQ,+CAA+C;SAC1G,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAC9C,iBAAiB,EAAE,SAAS;YAC5B,kBAAkB,EAAE,IAAI;YACxB,WAAW,EACT,mEAAmE;gBACnE,6EAA6E;gBAC7E,+EAA+E;gBAC/E,wDAAwD;SAC3D,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAChD,iBAAiB,EAAE,WAAW;YAC9B,kBAAkB,EAAE,IAAI;YACxB,WAAW,EAAE,uDAAuD;SACrE,CAAC,CAAC;QAEH,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACpD,iBAAiB,EAAE,eAAe;YAClC,WAAW,EACT,gFAAgF;gBAChF,gDAAgD;SACnD,CAAC,CAAC;QAEH,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC1D,iBAAiB,EAAE,8BAA8B;YACjD,YAAY,EAAE,MAAM;YACpB,WAAW,EACT,iGAAiG;gBACjG,6GAA6G;gBAC7G,wFAAwF;gBACxF,6GAA6G;gBAC7G,sEAAsE;SACzE,CAAC,CAAC;IACL,CAAC;IAEkB,KAAK,CAAC,cAAc;QACrC,MAAM,MAAM,GAAsB,IAAI,qCAAiB,EAAE,CAAC;QAC1D,IAAI,cAAsB,CAAC;QAE3B,IAAI,wBAAwB,GAAuB,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC;QACxF,IAAI,wBAAwB,EAAE,CAAC;YAC7B,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC;YAEpE,IAAI,8BAAU,CAAC,MAAM,CAAC,wBAAwB,CAAC,EAAE,CAAC;gBAChD,wBAAwB,GAAG,MAAM,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,CAAC;gBACnF,MAAM,6BAA6B,GAA6B,wBAAwB;oBACtF,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,wBAAwB,CAAC;oBACxD,CAAC,CAAC,SAAS,CAAC;gBACd,IAAI,CAAC,6BAA6B,EAAE,CAAC;oBACnC,MAAM,IAAI,KAAK,CACb,6BAA6B,IAAI,CAAC,yBAAyB,CAAC,QAAQ,8BAA8B,CACnG,CAAC;gBACJ,CAAC;qBAAM,IAAI,6BAA6B,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAC/D,MAAM,IAAI,KAAK,CACb,6BAA6B,IAAI,CAAC,yBAAyB,CAAC,QAAQ,gCAAgC;wBAClG,oBAAoB,CACvB,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,6BAA6B,IAAI,CAAC,yBAAyB,CAAC,QAAQ,4BAA4B,CACjG,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,eAAgC,CAAC;QAErC,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;YACpC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,8BAAU,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC/E,CAAC;YAED,eAAe,GAAG,iCAAe,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;QACvE,CAAC;aAAM,CAAC;YACN,MAAM,cAAc,GAA+C,iCAAe,CAAC,gBAAgB,CAAC;gBAClG,cAAc,EAAE,GAAG;aACpB,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,CAAC;gBAC5D,MAAM,IAAI,KAAK,CAAC,qBAAqB,iCAAe,CAAC,QAAQ,OAAO,CAAC,CAAC;YACxE,CAAC;YAED,MAAM,qBAAqB,GAAW,wBAAI,CAAC,eAAe,CAAC;gBACzD,aAAa,EAAE,cAAc,CAAC,oBAAoB;gBAClD,UAAU,EAAE,OAAO,CAAC,GAAG,EAAE;aAC1B,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,4BAA4B,qBAAqB,EAAE,CAAC,CAAC;YAEjE,eAAe,GAAG,iCAAe,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,eAAe,GAAoB,qBAAS,CAAC,MAAM,CAAC,eAAe,EAAE;YACzE,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK;YACtC,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK;YACjD,eAAe,EAAE,IAAI,CAAC,qBAAqB,CAAC,KAAK;YACjD,wBAAwB,EAAE,wBAAwB;SACnD,CAAC,CAAC;QAEH,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,sCAAsC,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,IAAI,eAAe,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,mBAAQ,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC,CAAC;YAC5E,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,mBAAQ,CAAC,MAAM,CAAC,uCAAuC,CAAC,CAAC,CAAC;YACjF,CAAC;YACD,MAAM,IAAI,wCAAoB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;CACF;AAnID,8BAmIC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as os from 'os';\nimport * as path from 'path';\nimport {\n  PackageJsonLookup,\n  FileSystem,\n  type IPackageJson,\n  Path,\n  AlreadyReportedError\n} from '@rushstack/node-core-library';\nimport { Colorize } from '@rushstack/terminal';\nimport {\n  CommandLineAction,\n  type CommandLineStringParameter,\n  type CommandLineFlagParameter\n} from '@rushstack/ts-command-line';\n\nimport { Extractor, type ExtractorResult } from '../api/Extractor';\nimport type { ApiExtractorCommandLine } from './ApiExtractorCommandLine';\nimport { ExtractorConfig, type IExtractorConfigPrepareOptions } from '../api/ExtractorConfig';\n\nexport class RunAction extends CommandLineAction {\n  private readonly _configFileParameter: CommandLineStringParameter;\n  private readonly _localParameter: CommandLineFlagParameter;\n  private readonly _verboseParameter: CommandLineFlagParameter;\n  private readonly _diagnosticsParameter: CommandLineFlagParameter;\n  private readonly _typescriptCompilerFolder: CommandLineStringParameter;\n\n  public constructor(parser: ApiExtractorCommandLine) {\n    super({\n      actionName: 'run',\n      summary: 'Invoke API Extractor on a project',\n      documentation: 'Invoke API Extractor on a project'\n    });\n\n    this._configFileParameter = this.defineStringParameter({\n      parameterLongName: '--config',\n      parameterShortName: '-c',\n      argumentName: 'FILE',\n      description: `Use the specified ${ExtractorConfig.FILENAME} file path, rather than guessing its location`\n    });\n\n    this._localParameter = this.defineFlagParameter({\n      parameterLongName: '--local',\n      parameterShortName: '-l',\n      description:\n        'Indicates that API Extractor is running as part of a local build,' +\n        \" e.g. on a developer's machine. This disables certain validation that would\" +\n        ' normally be performed for a ship/production build. For example, the *.api.md' +\n        ' report file is automatically copied in a local build.'\n    });\n\n    this._verboseParameter = this.defineFlagParameter({\n      parameterLongName: '--verbose',\n      parameterShortName: '-v',\n      description: 'Show additional informational messages in the output.'\n    });\n\n    this._diagnosticsParameter = this.defineFlagParameter({\n      parameterLongName: '--diagnostics',\n      description:\n        'Show diagnostic messages used for troubleshooting problems with API Extractor.' +\n        '  This flag also enables the \"--verbose\" flag.'\n    });\n\n    this._typescriptCompilerFolder = this.defineStringParameter({\n      parameterLongName: '--typescript-compiler-folder',\n      argumentName: 'PATH',\n      description:\n        'API Extractor uses its own TypeScript compiler engine to analyze your project.  If your project' +\n        ' is built with a significantly different TypeScript version, sometimes API Extractor may report compilation' +\n        ' errors due to differences in the system typings (e.g. lib.dom.d.ts).  You can use the' +\n        ' \"--typescriptCompilerFolder\" option to specify the folder path where you installed the TypeScript package,' +\n        \" and API Extractor's compiler will use those system typings instead.\"\n    });\n  }\n\n  protected override async onExecuteAsync(): Promise<void> {\n    const lookup: PackageJsonLookup = new PackageJsonLookup();\n    let configFilename: string;\n\n    let typescriptCompilerFolder: string | undefined = this._typescriptCompilerFolder.value;\n    if (typescriptCompilerFolder) {\n      typescriptCompilerFolder = path.normalize(typescriptCompilerFolder);\n\n      if (FileSystem.exists(typescriptCompilerFolder)) {\n        typescriptCompilerFolder = lookup.tryGetPackageFolderFor(typescriptCompilerFolder);\n        const typescriptCompilerPackageJson: IPackageJson | undefined = typescriptCompilerFolder\n          ? lookup.tryLoadPackageJsonFor(typescriptCompilerFolder)\n          : undefined;\n        if (!typescriptCompilerPackageJson) {\n          throw new Error(\n            `The path specified in the ${this._typescriptCompilerFolder.longName} parameter is not a package.`\n          );\n        } else if (typescriptCompilerPackageJson.name !== 'typescript') {\n          throw new Error(\n            `The path specified in the ${this._typescriptCompilerFolder.longName} parameter is not a TypeScript` +\n              ' compiler package.'\n          );\n        }\n      } else {\n        throw new Error(\n          `The path specified in the ${this._typescriptCompilerFolder.longName} parameter does not exist.`\n        );\n      }\n    }\n\n    let extractorConfig: ExtractorConfig;\n\n    if (this._configFileParameter.value) {\n      configFilename = path.normalize(this._configFileParameter.value);\n      if (!FileSystem.exists(configFilename)) {\n        throw new Error('Config file not found: ' + this._configFileParameter.value);\n      }\n\n      extractorConfig = ExtractorConfig.loadFileAndPrepare(configFilename);\n    } else {\n      const prepareOptions: IExtractorConfigPrepareOptions | undefined = ExtractorConfig.tryLoadForFolder({\n        startingFolder: '.'\n      });\n\n      if (!prepareOptions || !prepareOptions.configObjectFullPath) {\n        throw new Error(`Unable to find an ${ExtractorConfig.FILENAME} file`);\n      }\n\n      const configObjectShortPath: string = Path.formatConcisely({\n        pathToConvert: prepareOptions.configObjectFullPath,\n        baseFolder: process.cwd()\n      });\n      console.log(`Using configuration from ${configObjectShortPath}`);\n\n      extractorConfig = ExtractorConfig.prepare(prepareOptions);\n    }\n\n    const extractorResult: ExtractorResult = Extractor.invoke(extractorConfig, {\n      localBuild: this._localParameter.value,\n      showVerboseMessages: this._verboseParameter.value,\n      showDiagnostics: this._diagnosticsParameter.value,\n      typescriptCompilerFolder: typescriptCompilerFolder\n    });\n\n    if (extractorResult.succeeded) {\n      console.log(os.EOL + 'API Extractor completed successfully');\n    } else {\n      if (extractorResult.errorCount > 0) {\n        console.log(os.EOL + Colorize.red('API Extractor completed with errors'));\n      } else {\n        console.log(os.EOL + Colorize.yellow('API Extractor completed with warnings'));\n      }\n      throw new AlreadyReportedError();\n    }\n  }\n}\n"]}