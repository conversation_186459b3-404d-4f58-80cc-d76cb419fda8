// Auto-generated from tree-sitter Html v0.23.0
type HtmlTypes = {
  "attribute": {
    "type": "attribute",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "attribute_name",
          "named": true
        },
        {
          "type": "attribute_value",
          "named": true
        },
        {
          "type": "quoted_attribute_value",
          "named": true
        }
      ]
    }
  },
  "doctype": {
    "type": "doctype",
    "named": true,
    "fields": {}
  },
  "document": {
    "type": "document",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "doctype",
          "named": true
        },
        {
          "type": "element",
          "named": true
        },
        {
          "type": "entity",
          "named": true
        },
        {
          "type": "erroneous_end_tag",
          "named": true
        },
        {
          "type": "script_element",
          "named": true
        },
        {
          "type": "style_element",
          "named": true
        },
        {
          "type": "text",
          "named": true
        }
      ]
    }
  },
  "element": {
    "type": "element",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "doctype",
          "named": true
        },
        {
          "type": "element",
          "named": true
        },
        {
          "type": "end_tag",
          "named": true
        },
        {
          "type": "entity",
          "named": true
        },
        {
          "type": "erroneous_end_tag",
          "named": true
        },
        {
          "type": "script_element",
          "named": true
        },
        {
          "type": "self_closing_tag",
          "named": true
        },
        {
          "type": "start_tag",
          "named": true
        },
        {
          "type": "style_element",
          "named": true
        },
        {
          "type": "text",
          "named": true
        }
      ]
    }
  },
  "end_tag": {
    "type": "end_tag",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "tag_name",
          "named": true
        }
      ]
    }
  },
  "erroneous_end_tag": {
    "type": "erroneous_end_tag",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "erroneous_end_tag_name",
          "named": true
        }
      ]
    }
  },
  "quoted_attribute_value": {
    "type": "quoted_attribute_value",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "attribute_value",
          "named": true
        }
      ]
    }
  },
  "script_element": {
    "type": "script_element",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "end_tag",
          "named": true
        },
        {
          "type": "raw_text",
          "named": true
        },
        {
          "type": "start_tag",
          "named": true
        }
      ]
    }
  },
  "self_closing_tag": {
    "type": "self_closing_tag",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "attribute",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        }
      ]
    }
  },
  "start_tag": {
    "type": "start_tag",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "attribute",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        }
      ]
    }
  },
  "style_element": {
    "type": "style_element",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "end_tag",
          "named": true
        },
        {
          "type": "raw_text",
          "named": true
        },
        {
          "type": "start_tag",
          "named": true
        }
      ]
    }
  },
  "attribute_name": {
    "type": "attribute_name",
    "named": true
  },
  "attribute_value": {
    "type": "attribute_value",
    "named": true
  },
  "comment": {
    "type": "comment",
    "named": true
  },
  "entity": {
    "type": "entity",
    "named": true
  },
  "erroneous_end_tag_name": {
    "type": "erroneous_end_tag_name",
    "named": true
  },
  "raw_text": {
    "type": "raw_text",
    "named": true
  },
  "tag_name": {
    "type": "tag_name",
    "named": true
  },
  "text": {
    "type": "text",
    "named": true
  }
};
export default HtmlTypes;