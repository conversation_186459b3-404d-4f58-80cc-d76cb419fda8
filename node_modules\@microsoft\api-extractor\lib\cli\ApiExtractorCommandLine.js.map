{"version": 3, "file": "ApiExtractorCommandLine.js", "sourceRoot": "", "sources": ["../../src/cli/ApiExtractorCommandLine.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,uCAAyB;AAEzB,gEAA8F;AAC9F,oEAAmF;AACnF,kDAA+C;AAE/C,2CAAwC;AACxC,6CAA0C;AAE1C,MAAa,uBAAwB,SAAQ,mCAAiB;IAG5D;QACE,KAAK,CAAC;YACJ,YAAY,EAAE,eAAe;YAC7B,eAAe,EACb,wFAAwF;gBACxF,0GAA0G;gBAC1G,qGAAqG;gBACrG,oGAAoG;gBACpG,wEAAwE;SAC3E,CAAC,CAAC;QACH,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAC9C,iBAAiB,EAAE,SAAS;YAC5B,kBAAkB,EAAE,IAAI;YACxB,WAAW,EAAE,sEAAsE;SACpF,CAAC,CAAC;IACL,CAAC;IAEkB,KAAK,CAAC,cAAc;QACrC,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;YAC/B,iCAAa,CAAC,eAAe,GAAG,IAAI,CAAC;QACvC,CAAC;QAED,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC;YACH,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;YAC7B,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,CAAC,KAAK,YAAY,wCAAoB,CAAC,EAAE,CAAC;gBAC7C,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;oBAC/B,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,mBAAQ,CAAC,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;gBACzE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,uBAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,IAAI,qBAAS,CAAC,IAAI,CAAC,CAAC,CAAC;IACtC,CAAC;CACF;AA9CD,0DA8CC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as os from 'os';\n\nimport { CommandLineParser, type CommandLineFlagParameter } from '@rushstack/ts-command-line';\nimport { AlreadyReportedError, InternalError } from '@rushstack/node-core-library';\nimport { Colorize } from '@rushstack/terminal';\n\nimport { RunAction } from './RunAction';\nimport { InitAction } from './InitAction';\n\nexport class ApiExtractorCommandLine extends CommandLineParser {\n  private readonly _debugParameter: CommandLineFlagParameter;\n\n  public constructor() {\n    super({\n      toolFilename: 'api-extractor',\n      toolDescription:\n        'API Extractor helps you build better TypeScript libraries.  It analyzes the main entry' +\n        ' point for your package, collects the inventory of exported declarations, and then generates three kinds' +\n        ' of output:  an API report file (.api.md) to facilitate reviews, a declaration rollup (.d.ts) to be' +\n        ' published with your NPM package, and a doc model file (.api.json) to be used with a documentation' +\n        ' tool such as api-documenter.  For details, please visit the web site.'\n    });\n    this._populateActions();\n\n    this._debugParameter = this.defineFlagParameter({\n      parameterLongName: '--debug',\n      parameterShortName: '-d',\n      description: 'Show the full call stack if an error occurs while executing the tool'\n    });\n  }\n\n  protected override async onExecuteAsync(): Promise<void> {\n    if (this._debugParameter.value) {\n      InternalError.breakInDebugger = true;\n    }\n\n    process.exitCode = 1;\n    try {\n      await super.onExecuteAsync();\n      process.exitCode = 0;\n    } catch (error) {\n      if (!(error instanceof AlreadyReportedError)) {\n        if (this._debugParameter.value) {\n          console.error(os.EOL + error.stack);\n        } else {\n          console.error(os.EOL + Colorize.red('ERROR: ' + error.message.trim()));\n        }\n      }\n    }\n  }\n\n  private _populateActions(): void {\n    this.addAction(new InitAction(this));\n    this.addAction(new RunAction(this));\n  }\n}\n"]}