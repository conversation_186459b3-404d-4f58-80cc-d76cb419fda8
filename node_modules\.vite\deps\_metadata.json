{"hash": "4a69622e", "configHash": "f0b9a5bf", "lockfileHash": "ba19b42e", "browserHash": "63e8545a", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "c7eafca6", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c61fb003", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "10ecb231", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "fecb9f3e", "needsInterop": true}, "date-fns": {"src": "../../date-fns/index.js", "file": "date-fns.js", "fileHash": "aa6590f1", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ba2801df", "needsInterop": true}, "react-icons/bi": {"src": "../../react-icons/bi/index.mjs", "file": "react-icons_bi.js", "fileHash": "7a0bb25a", "needsInterop": false}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "2c83ffd2", "needsInterop": false}, "react-icons/fa6": {"src": "../../react-icons/fa6/index.mjs", "file": "react-icons_fa6.js", "fileHash": "f00c9523", "needsInterop": false}, "react-icons/gi": {"src": "../../react-icons/gi/index.mjs", "file": "react-icons_gi.js", "fileHash": "01d1b08e", "needsInterop": false}, "react-icons/gr": {"src": "../../react-icons/gr/index.mjs", "file": "react-icons_gr.js", "fileHash": "2cd9be7d", "needsInterop": false}, "react-icons/hi": {"src": "../../react-icons/hi/index.mjs", "file": "react-icons_hi.js", "fileHash": "e0e9d289", "needsInterop": false}, "react-icons/md": {"src": "../../react-icons/md/index.mjs", "file": "react-icons_md.js", "fileHash": "78472d3c", "needsInterop": false}}, "chunks": {"chunk-N4JV66NV": {"file": "chunk-N4JV66NV.js"}, "chunk-TJONBCBV": {"file": "chunk-TJONBCBV.js"}, "chunk-CBG3MKAY": {"file": "chunk-CBG3MKAY.js"}, "chunk-EQCVQC35": {"file": "chunk-EQCVQC35.js"}}}