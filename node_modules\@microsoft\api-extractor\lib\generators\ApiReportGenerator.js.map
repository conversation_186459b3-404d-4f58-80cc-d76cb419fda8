{"version": 3, "file": "ApiReportGenerator.js", "sourceRoot": "", "sources": ["../../src/generators/ApiReportGenerator.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,+CAAiC;AACjC,oEAAmE;AACnE,wEAA4D;AAE5D,sDAAmD;AACnD,qEAAkE;AAClE,2CAAwC;AAExC,+DAA4D;AAE5D,qDAAkD;AAClD,qDAAkD;AAElD,qDAAkD;AAClD,qDAAkD;AAClD,uEAAoE;AAGpE,yFAAsF;AACtF,kEAA+D;AAI/D,MAAa,kBAAkB;IAG7B;;;;;;OAMG;IACI,MAAM,CAAC,4BAA4B,CACxC,iBAAyB,EACzB,mBAA2B;QAE3B,wCAAwC;QACxC,MAAM,gBAAgB,GAAW,iBAAiB,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC1E,MAAM,kBAAkB,GAAW,mBAAmB,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC9E,OAAO,gBAAgB,KAAK,kBAAkB,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,yBAAyB,CAAC,SAAoB,EAAE,aAA+B;;QAC3F,MAAM,MAAM,GAAmB,IAAI,+BAAc,EAAE,CAAC;QACpD,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAEhC,SAAS,qBAAqB,CAAC,KAAa;YAC1C,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAiB,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;QAChF,CAAC;QAED,2FAA2F;QAC3F,MAAM,kBAAkB,GACtB,aAAa,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC;QACjF,MAAM,CAAC,SAAS,CACd;YACE,MAAM,kBAAkB,wBAAwB,SAAS,CAAC,cAAc,CAAC,IAAI,GAAG;YAChF,EAAE;YACF,mGAAmG;YACnG,EAAE;SACH,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;QAEF,0DAA0D;QAC1D,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE5B,mCAAmC;QACnC,KAAK,MAAM,sBAAsB,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;YAC7F,gIAAgI;YAChI,MAAM,CAAC,SAAS,CAAC,yBAAyB,sBAAsB,MAAM,CAAC,CAAC;QAC1E,CAAC;QACD,KAAK,MAAM,qBAAqB,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;YAC3F,MAAM,CAAC,SAAS,CAAC,uBAAuB,qBAAqB,MAAM,CAAC,CAAC;QACvE,CAAC;QACD,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAE3B,mBAAmB;QACnB,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxC,IAAI,MAAM,CAAC,SAAS,YAAY,qBAAS,EAAE,CAAC;gBAC1C,+BAAc,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QACD,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAE3B,gCAAgC;QAChC,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxC,MAAM,SAAS,GAAc,MAAM,CAAC,SAAS,CAAC;YAC9C,MAAM,cAAc,GAA+B,SAAS,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC;YACrG,MAAM,sBAAsB,GAAe,MAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,sBAAsB,mCAAI,gCAAU,CAAC,IAAI,CAAC;YAErG,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,EAAE,aAAa,CAAC,EAAE,CAAC;gBAC1E,SAAS;YACX,CAAC;YAED,IAAI,MAAM,CAAC,UAAU,IAAI,SAAS,CAAC,eAAe,CAAC,gCAAgC,EAAE,CAAC;gBAQpF,MAAM,aAAa,GAA+B,IAAI,GAAG,EAAyB,CAAC;gBAEnF,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;oBAC5C,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;wBAC/B,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,UAAU,EAAE,kBAAkB,EAAE,EAAE,EAAE,CAAC,CAAC;oBACxE,CAAC;gBACH,CAAC;gBAED,IAAI,SAAS,YAAY,qBAAS,EAAE,CAAC;oBACnC,4CAA4C;oBAC5C,KAAK,MAAM,cAAc,IAAI,SAAS,CAAC,eAAe,IAAI,EAAE,EAAE,CAAC;wBAC7D,oDAAoD;wBACpD,MAAM,eAAe,GACnB,SAAS,CAAC,aAAa,CAAC,oCAAoC,CAAC,cAAc,CAAC,CAAC;wBAE/E,2EAA2E;wBAC3E,4FAA4F;wBAC5F,oGAAoG;wBACpG,MAAM,gBAAgB,GAAuB,EAAE,CAAC;wBAChD,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;4BACtC,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gCAClC,MAAM,YAAY,GAA8B,aAAa,CAAC,GAAG,CAC/D,OAAO,CAAC,UAAU,CAAC,UAAU,CAC9B,CAAC;gCACF,IAAI,YAAY,EAAE,CAAC;oCACjB,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oCAC9C,SAAS;gCACX,CAAC;4BACH,CAAC;4BACD,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBACjC,CAAC;wBAED,IAAI,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,cAAc,EAAE,aAAa,CAAC,EAAE,CAAC;4BAC7E,MAAM,CAAC,iBAAiB,EAAE,CAAC;4BAC3B,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,SAAS,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC,CAAC;4BAEhG,MAAM,IAAI,GAAS,IAAI,WAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;4BAExD,MAAM,eAAe,GAAoB,SAAS,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;4BACxF,IAAI,eAAe,CAAC,aAAa,EAAE,CAAC;gCAClC,kBAAkB,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;4BACrD,CAAC;iCAAM,CAAC;gCACN,kBAAkB,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,cAAc,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;4BAChG,CAAC;4BAED,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;4BAC/B,MAAM,CAAC,aAAa,EAAE,CAAC;wBACzB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,SAAS,YAAY,uCAAkB,EAAE,CAAC;oBAC5C,MAAM,mBAAmB,GAAyB,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;oBAEhG,IAAI,MAAM,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;wBACrC,2BAA2B;wBAC3B,MAAM,IAAI,iCAAa,CAAC,0CAA0C,CAAC,CAAC;oBACtE,CAAC;oBAED,IAAI,mBAAmB,CAAC,2BAA2B,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;wBAC7D,iFAAiF;wBACjF,MAAM,IAAI,KAAK,CACb,OAAO,MAAM,CAAC,WAAW,qEAAqE;4BAC5F,yDAA2B,CAAC,iBAAiB,CAAC,SAAS,CAAC,WAAW,CAAC,CACvE,CAAC;oBACJ,CAAC;oBAED,2EAA2E;oBAC3E,EAAE;oBACF,iCAAiC;oBACjC,gBAAgB;oBAChB,aAAa;oBACb,YAAY;oBACZ,SAAS;oBACT,OAAO;oBACP,EAAE;oBACF,8FAA8F;oBAC9F,kFAAkF;oBAElF,MAAM,CAAC,iBAAiB,EAAE,CAAC;oBAC3B,MAAM,CAAC,SAAS,CAAC,qBAAqB,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC;oBAE9D,2FAA2F;oBAC3F,MAAM,CAAC,cAAc,EAAE,CAAC;oBACxB,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;oBAC7B,MAAM,CAAC,cAAc,EAAE,CAAC;oBAExB,MAAM,aAAa,GAAa,EAAE,CAAC;oBACnC,KAAK,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,IAAI,mBAAmB,CAAC,qBAAqB,EAAE,CAAC;wBACvF,MAAM,eAAe,GACnB,SAAS,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;wBAClD,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;4BAClC,2BAA2B;4BAC3B,0FAA0F;4BAC1F,MAAM,IAAI,iCAAa,CACrB,oCAAoC,MAAM,CAAC,WAAW,IAAI,cAAc,CAAC,SAAS,EAAE,CACrF,CAAC;wBACJ,CAAC;wBAED,IAAI,eAAe,CAAC,WAAW,KAAK,YAAY,EAAE,CAAC;4BACjD,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;wBAClD,CAAC;6BAAM,CAAC;4BACN,aAAa,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,WAAW,OAAO,YAAY,EAAE,CAAC,CAAC;wBAC1E,CAAC;oBACH,CAAC;oBACD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBAE5C,MAAM,CAAC,cAAc,EAAE,CAAC;oBACxB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,0BAA0B;oBACjD,MAAM,CAAC,cAAc,EAAE,CAAC;oBACxB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,qCAAqC;gBAC9D,CAAC;gBAED,kDAAkD;gBAClD,KAAK,MAAM,YAAY,IAAI,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;oBAClD,gCAAgC;oBAChC,IAAI,YAAY,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC/C,MAAM,CAAC,iBAAiB,EAAE,CAAC;wBAC3B,KAAK,MAAM,OAAO,IAAI,YAAY,CAAC,kBAAkB,EAAE,CAAC;4BACtD,kBAAkB,CAAC,oBAAoB,CACrC,MAAM,EACN,WAAW,GAAG,OAAO,CAAC,4BAA4B,EAAE,CACrD,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,+BAAc,CAAC,eAAe,CAAC,MAAM,EAAE,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBAC1E,CAAC;gBACD,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,+BAAc,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAElD,4DAA4D;QAC5D,MAAM,oBAAoB,GACxB,SAAS,CAAC,aAAa,CAAC,sCAAsC,EAAE,CAAC;QACnE,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC3B,kBAAkB,CAAC,oBAAoB,CAAC,MAAM,EAAE,4CAA4C,CAAC,CAAC;YAC9F,kBAAkB,CAAC,oBAAoB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YACpD,KAAK,MAAM,mBAAmB,IAAI,oBAAoB,EAAE,CAAC;gBACvD,kBAAkB,CAAC,oBAAoB,CACrC,MAAM,EACN,mBAAmB,CAAC,yBAAyB,CAAC,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,CACtF,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,SAAS,CAAC,cAAc,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACxD,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC3B,kBAAkB,CAAC,oBAAoB,CAAC,MAAM,EAAE,qDAAqD,CAAC,CAAC;QACzG,CAAC;QAED,0DAA0D;QAC1D,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAC3B,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAExB,6BAA6B;QAC7B,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,WAAW,CACxB,SAAoB,EACpB,IAAU,EACV,MAAuB,EACvB,cAA8B,EAC9B,iBAA0B,EAC1B,aAA+B;QAE/B,6CAA6C;QAC7C,sCAAsC;QACtC,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,SAAS,EAAE,cAAc,EAAE,aAAa,CAAC,EAAE,CAAC;YAC5F,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAqB,IAAI,CAAC,eAAe,CAAC;QAE5D,IAAI,eAAe,GAAY,IAAI,CAAC;QACpC,IAAI,YAAY,GAAY,KAAK,CAAC;QAElC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY;gBAC7B,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC5B,yDAAyD;gBACzD,eAAe,GAAG,KAAK,CAAC;gBACxB,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;YACjC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC;YAClC,KAAK,EAAE,CAAC,UAAU,CAAC,cAAc;gBAC/B,kFAAkF;gBAClF,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;YACpC,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC;YAChC,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;YACpC,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC;YACjC,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAC/B,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe;gBAChC,8CAA8C;gBAC9C,IAAI,iBAAiB,GAAW,EAAE,CAAC;gBAEnC,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBAC9B,iBAAiB,GAAG,SAAS,GAAG,iBAAiB,CAAC;gBACpD,CAAC;gBAED,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;oBACnE,2FAA2F;oBAC3F,uCAAuC;oBACvC,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,iBAAiB,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC1F,CAAC;qBAAM,CAAC;oBACN,gDAAgD;oBAChD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC1E,CAAC;gBACD,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAChB,IAAI,+BAAc,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC3D,kGAAkG;wBAClG,wCAAwC;wBACxC,YAAY,GAAG,IAAI,CAAC;oBACtB,CAAC;yBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;wBAC1D,iGAAiG;wBACjG,YAAY,GAAG,IAAI,CAAC;oBACtB,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;gBACpC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACjB,6EAA6E;oBAC7E,0EAA0E;oBAC1E,qEAAqE;oBACrE,EAAE;oBACF,qFAAqF;oBACrF,gFAAgF;oBAChF,4CAA4C;oBAC5C,MAAM,IAAI,GAA2C,qCAAiB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE;wBAC9F,EAAE,CAAC,UAAU,CAAC,uBAAuB;wBACrC,EAAE,CAAC,UAAU,CAAC,mBAAmB;qBAClC,CAAC,CAAC;oBACH,IAAI,CAAC,IAAI,EAAE,CAAC;wBACV,iEAAiE;wBACjE,MAAM,IAAI,iCAAa,CAAC,kCAAkC,CAAC,CAAC;oBAC9D,CAAC;oBACD,MAAM,UAAU,GAAW,IAAI;yBAC5B,aAAa,EAAE;yBACf,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACpE,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;oBACjE,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,CAAC;oBAE/B,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;wBAC9B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;oBAClE,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,MAAM,gBAAgB,GAAgC,SAAS,CAAC,mBAAmB,CACjF,IAAI,CAAC,IAAqB,CAC3B,CAAC;gBAEF,IAAI,gBAAgB,EAAE,CAAC;oBACrB,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;wBAClC,2BAA2B;wBAC3B,MAAM,IAAI,iCAAa,CAAC,0CAA0C,CAAC,CAAC;oBACtE,CAAC;oBAED,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,gBAAgB,CAAC,WAAW,CAAC;oBACxD,iBAAiB;oBACjB,2CAA2C;gBAC7C,CAAC;qBAAM,CAAC;oBACN,iBAAiB;oBACjB,4CAA4C;gBAC9C,CAAC;gBAED,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW;gBAC5B,iBAAiB,GAAG,IAAI,CAAC;gBACzB,MAAM;YAER,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,+BAAc,CAAC,oBAAoB,CACjC,SAAS,EACT,IAAI,EACJ,cAAc,EACd,CAAC,SAAS,EAAE,mBAAmB,EAAE,EAAE;oBACjC,kBAAkB,CAAC,WAAW,CAC5B,SAAS,EACT,SAAS,EACT,MAAM,EACN,mBAAmB,EACnB,iBAAiB,EACjB,aAAa,CACd,CAAC;gBACJ,CAAC,CACF,CAAC;gBACF,MAAM;QACV,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACpB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,IAAI,mBAAmB,GAAmB,cAAc,CAAC;gBAEzD,IAAI,+BAAc,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrD,mBAAmB,GAAG,SAAS,CAAC,cAAc,CAAC,4BAA4B,CACzE,KAAK,CAAC,IAAI,EACV,cAAc,CACf,CAAC;oBAEF,IAAI,kBAAkB,CAAC,yBAAyB,CAAC,SAAS,EAAE,mBAAmB,EAAE,aAAa,CAAC,EAAE,CAAC;wBAChG,IAAI,YAAY,EAAE,CAAC;4BACjB,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC;4BACtC,KAAK,CAAC,YAAY,CAAC,OAAO,GAAG,qBAAS,CAAC,4BAA4B,CACjE,mBAAmB,CAAC,SAAS,CAAC,SAAS,CACxC,CAAC;wBACJ,CAAC;wBAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;4BACvB,MAAM,gBAAgB,GACpB,SAAS,CAAC,aAAa,CAAC,oCAAoC,CAAC,mBAAmB,CAAC,CAAC;4BAEpF,iEAAiE;4BACjE,MAAM,aAAa,GAAW,kBAAkB,CAAC,iBAAiB,CAChE,SAAS,EACT,mBAAmB,EACnB,gBAAgB,CACjB,CAAC;4BAEF,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;wBACxE,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,kBAAkB,CAAC,WAAW,CAC5B,SAAS,EACT,KAAK,EACL,MAAM,EACN,mBAAmB,EACnB,iBAAiB,EACjB,aAAa,CACd,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,yBAAyB,CACtC,SAAoB,EACpB,cAA8B,EAC9B,aAA+B;QAE/B,0DAA0D;QAC1D,sCAAsC;QACtC,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACpE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,eAAe,GAAoB,SAAS,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAExF,OAAO,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;IAC3F,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,UAAsB,EAAE,aAA+B;QAC7F,QAAQ,aAAa,EAAE,CAAC;YACtB,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC;YACd,KAAK,OAAO;gBACV,OAAO,CACL,UAAU,KAAK,gCAAU,CAAC,KAAK;oBAC/B,UAAU,KAAK,gCAAU,CAAC,IAAI;oBAC9B,UAAU,KAAK,gCAAU,CAAC,MAAM;oBAChC,qEAAqE;oBACrE,UAAU,KAAK,gCAAU,CAAC,IAAI,CAC/B,CAAC;YACJ,KAAK,MAAM;gBACT,OAAO,CACL,UAAU,KAAK,gCAAU,CAAC,IAAI;oBAC9B,UAAU,KAAK,gCAAU,CAAC,MAAM;oBAChC,qEAAqE;oBACrE,UAAU,KAAK,gCAAU,CAAC,IAAI,CAC/B,CAAC;YACJ,KAAK,QAAQ;gBACX,OAAO,CACL,UAAU,KAAK,gCAAU,CAAC,MAAM;oBAChC,qEAAqE;oBACrE,UAAU,KAAK,gCAAU,CAAC,IAAI,CAC/B,CAAC;YACJ;gBACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,aAAa,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CAAC,IAAU;QACjD,6BAA6B;QAC7B,EAAE;QACF,sBAAsB;QACtB,kBAAkB;QAClB,6CAA6C;QAC7C,+CAA+C;QAC/C,yCAAyC;QACzC,mDAAmD;QACnD,gDAAgD;QAChD,kBAAkB;QAClB,YAAY;QACZ,gCAAgC;QAChC,EAAE;QACF,WAAW;QACX,uBAAuB;QACvB,kBAAkB;QAClB,6CAA6C;QAC7C,+CAA+C;QAC/C,iDAAiD;QACjD,uDAAuD;QACvD,mBAAmB;QACnB,kDAAkD;QAClD,oBAAoB;QACpB,cAAc;QACd,kCAAkC;QAClC,EAAE;QACF,wCAAwC;QACxC,EAAE;QACF,gCAAgC;QAChC,oDAAoD;QACpD,EAAE;QAEF,IAAI,QAAQ,GAAY,KAAK,CAAC;QAC9B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;gBACrG,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC/B,CAAC;YACD,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC5C,QAAQ,GAAG,IAAI,CAAC;gBAChB,KAAK,CAAC,YAAY,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC7C,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,0BAA0B,CAAC;YACzD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,iBAAiB,CAC9B,SAAoB,EACpB,cAA8B,EAC9B,gBAAoC;;QAEpC,MAAM,MAAM,GAAmB,IAAI,+BAAc,EAAE,CAAC;QAEpD,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;YACvC,kBAAkB,CAAC,oBAAoB,CAAC,MAAM,EAAE,WAAW,GAAG,OAAO,CAAC,4BAA4B,EAAE,CAAC,CAAC;QACxG,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,cAAc,CAAC,EAAE,CAAC;YACtD,MAAM,WAAW,GAAa,EAAE,CAAC;YACjC,MAAM,eAAe,GAAoB,SAAS,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAExF,8BAA8B;YAC9B,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC;gBAC5C,IAAI,eAAe,CAAC,mBAAmB,KAAK,gCAAU,CAAC,IAAI,EAAE,CAAC;oBAC5D,WAAW,CAAC,IAAI,CAAC,gCAAU,CAAC,UAAU,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBAC/E,CAAC;YACH,CAAC;YAED,qGAAqG;YACrG,+FAA+F;YAC/F,8GAA8G;YAC9G,iCAAiC;YACjC,MAAM,EACJ,SAAS,EAAE,eAAe,EAC1B,UAAU,EAAE,gBAAgB,EAC5B,WAAW,EAAE,iBAAiB,EAC9B,gBAAgB,EAAE,sBAAsB,EACxC,aAAa,EAAE,mBAAmB,EAClC,GAAG,iBAAiB,EACrB,GAAG,SAAS,CAAC,eAAe,CAAC,YAAY,CAAC;YAE3C,qGAAqG;YACrG,IAAI,eAAe,IAAI,eAAe,CAAC,QAAQ,EAAE,CAAC;gBAChD,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9B,CAAC;YACD,IAAI,gBAAgB,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;gBAClD,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/B,CAAC;YACD,IAAI,iBAAiB,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;gBACpD,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChC,CAAC;YACD,IAAI,sBAAsB,IAAI,eAAe,CAAC,eAAe,EAAE,CAAC;gBAC9D,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrC,CAAC;YACD,IAAI,mBAAmB,KAAI,MAAA,eAAe,CAAC,YAAY,0CAAE,eAAe,CAAA,EAAE,CAAC;gBACzE,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClC,CAAC;YAED,+FAA+F;YAC/F,KAAK,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACjE,IAAI,SAAS,EAAE,CAAC;oBACd,gFAAgF;oBAChF,IAAI,MAAA,eAAe,CAAC,YAAY,0CAAE,YAAY,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC;wBAC/F,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACxB,CAAC;yBAAM,IAAI,MAAA,eAAe,CAAC,YAAY,0CAAE,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBACxE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACxB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,uEAAuE;YACvE,IAAI,eAAe,CAAC,YAAY,EAAE,CAAC;gBACjC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAEnC,SAAS,CAAC,aAAa,CAAC,gBAAgB,CACtC,uCAAkB,CAAC,YAAY,EAC/B,8BAA8B,cAAc,CAAC,SAAS,CAAC,SAAS,IAAI,EACpE,cAAc,CACf,CAAC;YACJ,CAAC;YAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChC,kBAAkB,CAAC,oBAAoB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,iCAAiC;gBACxF,CAAC;gBAED,kBAAkB,CAAC,oBAAoB,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,MAAsB,EAAE,IAAY;QACtE,MAAM,KAAK,GAAa,wBAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3D,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE,CAAC;YAC7B,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACvB,MAAM,CAAC,SAAS,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;;AA1nBH,gDA2nBC;AA1nBgB,oCAAiB,GAAW,OAAO,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport * as ts from 'typescript';\nimport { Text, InternalError } from '@rushstack/node-core-library';\nimport { ReleaseTag } from '@microsoft/api-extractor-model';\n\nimport { Collector } from '../collector/Collector';\nimport { TypeScriptHelpers } from '../analyzer/TypeScriptHelpers';\nimport { Span } from '../analyzer/Span';\nimport type { CollectorEntity } from '../collector/CollectorEntity';\nimport { AstDeclaration } from '../analyzer/AstDeclaration';\nimport type { ApiItemMetadata } from '../collector/ApiItemMetadata';\nimport { AstImport } from '../analyzer/AstImport';\nimport { AstSymbol } from '../analyzer/AstSymbol';\nimport type { ExtractorMessage } from '../api/ExtractorMessage';\nimport { IndentedWriter } from './IndentedWriter';\nimport { DtsEmitHelpers } from './DtsEmitHelpers';\nimport { AstNamespaceImport } from '../analyzer/AstNamespaceImport';\nimport type { AstEntity } from '../analyzer/AstEntity';\nimport type { IAstModuleExportInfo } from '../analyzer/AstModule';\nimport { SourceFileLocationFormatter } from '../analyzer/SourceFileLocationFormatter';\nimport { ExtractorMessageId } from '../api/ExtractorMessageId';\nimport type { ApiReportVariant } from '../api/IConfigFile';\nimport type { SymbolMetadata } from '../collector/SymbolMetadata';\n\nexport class ApiReportGenerator {\n  private static _trimSpacesRegExp: RegExp = / +$/gm;\n\n  /**\n   * Compares the contents of two API files that were created using ApiFileGenerator,\n   * and returns true if they are equivalent.  Note that these files are not normally edited\n   * by a human; the \"equivalence\" comparison here is intended to ignore spurious changes that\n   * might be introduced by a tool, e.g. Git newline normalization or an editor that strips\n   * whitespace when saving.\n   */\n  public static areEquivalentApiFileContents(\n    actualFileContent: string,\n    expectedFileContent: string\n  ): boolean {\n    // NOTE: \"\\s\" also matches \"\\r\" and \"\\n\"\n    const normalizedActual: string = actualFileContent.replace(/[\\s]+/g, ' ');\n    const normalizedExpected: string = expectedFileContent.replace(/[\\s]+/g, ' ');\n    return normalizedActual === normalizedExpected;\n  }\n\n  /**\n   * Generates and returns the API report contents as a string.\n   *\n   * @param reportVariant - The release level with which the report is associated.\n   * Can also be viewed as the minimal release level of items that should be included in the report.\n   */\n  public static generateReviewFileContent(collector: Collector, reportVariant: ApiReportVariant): string {\n    const writer: IndentedWriter = new IndentedWriter();\n    writer.trimLeadingSpaces = true;\n\n    function capitalizeFirstLetter(input: string): string {\n      return input === '' ? '' : `${input[0].toLocaleUpperCase()}${input.slice(1)}`;\n    }\n\n    // For backwards compatibility, don't emit \"complete\" in report text for untrimmed reports.\n    const releaseLevelPrefix: string =\n      reportVariant === 'complete' ? '' : `${capitalizeFirstLetter(reportVariant)} `;\n    writer.writeLine(\n      [\n        `## ${releaseLevelPrefix}API Report File for \"${collector.workingPackage.name}\"`,\n        ``,\n        `> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).`,\n        ``\n      ].join('\\n')\n    );\n\n    // Write the opening delimiter for the Markdown code fence\n    writer.writeLine('```ts\\n');\n\n    // Emit the triple slash directives\n    for (const typeDirectiveReference of Array.from(collector.dtsTypeReferenceDirectives).sort()) {\n      // https://github.com/microsoft/TypeScript/blob/611ebc7aadd7a44a4c0447698bfda9222a78cb66/src/compiler/declarationEmitter.ts#L162\n      writer.writeLine(`/// <reference types=\"${typeDirectiveReference}\" />`);\n    }\n    for (const libDirectiveReference of Array.from(collector.dtsLibReferenceDirectives).sort()) {\n      writer.writeLine(`/// <reference lib=\"${libDirectiveReference}\" />`);\n    }\n    writer.ensureSkippedLine();\n\n    // Emit the imports\n    for (const entity of collector.entities) {\n      if (entity.astEntity instanceof AstImport) {\n        DtsEmitHelpers.emitImport(writer, entity, entity.astEntity);\n      }\n    }\n    writer.ensureSkippedLine();\n\n    // Emit the regular declarations\n    for (const entity of collector.entities) {\n      const astEntity: AstEntity = entity.astEntity;\n      const symbolMetadata: SymbolMetadata | undefined = collector.tryFetchMetadataForAstEntity(astEntity);\n      const maxEffectiveReleaseTag: ReleaseTag = symbolMetadata?.maxEffectiveReleaseTag ?? ReleaseTag.None;\n\n      if (!this._shouldIncludeReleaseTag(maxEffectiveReleaseTag, reportVariant)) {\n        continue;\n      }\n\n      if (entity.consumable || collector.extractorConfig.apiReportIncludeForgottenExports) {\n        // First, collect the list of export names for this symbol.  When reporting messages with\n        // ExtractorMessage.properties.exportName, this will enable us to emit the warning comments alongside\n        // the associated export statement.\n        interface IExportToEmit {\n          readonly exportName: string;\n          readonly associatedMessages: ExtractorMessage[];\n        }\n        const exportsToEmit: Map<string, IExportToEmit> = new Map<string, IExportToEmit>();\n\n        for (const exportName of entity.exportNames) {\n          if (!entity.shouldInlineExport) {\n            exportsToEmit.set(exportName, { exportName, associatedMessages: [] });\n          }\n        }\n\n        if (astEntity instanceof AstSymbol) {\n          // Emit all the declarations for this entity\n          for (const astDeclaration of astEntity.astDeclarations || []) {\n            // Get the messages associated with this declaration\n            const fetchedMessages: ExtractorMessage[] =\n              collector.messageRouter.fetchAssociatedMessagesForReviewFile(astDeclaration);\n\n            // Peel off the messages associated with an export statement and store them\n            // in IExportToEmit.associatedMessages (to be processed later).  The remaining messages will\n            // added to messagesToReport, to be emitted next to the declaration instead of the export statement.\n            const messagesToReport: ExtractorMessage[] = [];\n            for (const message of fetchedMessages) {\n              if (message.properties.exportName) {\n                const exportToEmit: IExportToEmit | undefined = exportsToEmit.get(\n                  message.properties.exportName\n                );\n                if (exportToEmit) {\n                  exportToEmit.associatedMessages.push(message);\n                  continue;\n                }\n              }\n              messagesToReport.push(message);\n            }\n\n            if (this._shouldIncludeDeclaration(collector, astDeclaration, reportVariant)) {\n              writer.ensureSkippedLine();\n              writer.write(ApiReportGenerator._getAedocSynopsis(collector, astDeclaration, messagesToReport));\n\n              const span: Span = new Span(astDeclaration.declaration);\n\n              const apiItemMetadata: ApiItemMetadata = collector.fetchApiItemMetadata(astDeclaration);\n              if (apiItemMetadata.isPreapproved) {\n                ApiReportGenerator._modifySpanForPreapproved(span);\n              } else {\n                ApiReportGenerator._modifySpan(collector, span, entity, astDeclaration, false, reportVariant);\n              }\n\n              span.writeModifiedText(writer);\n              writer.ensureNewLine();\n            }\n          }\n        }\n\n        if (astEntity instanceof AstNamespaceImport) {\n          const astModuleExportInfo: IAstModuleExportInfo = astEntity.fetchAstModuleExportInfo(collector);\n\n          if (entity.nameForEmit === undefined) {\n            // This should never happen\n            throw new InternalError('referencedEntry.nameForEmit is undefined');\n          }\n\n          if (astModuleExportInfo.starExportedExternalModules.size > 0) {\n            // We could support this, but we would need to find a way to safely represent it.\n            throw new Error(\n              `The ${entity.nameForEmit} namespace import includes a star export, which is not supported:\\n` +\n                SourceFileLocationFormatter.formatDeclaration(astEntity.declaration)\n            );\n          }\n\n          // Emit a synthetic declaration for the namespace.  It will look like this:\n          //\n          //    declare namespace example {\n          //      export {\n          //        f1,\n          //        f2\n          //      }\n          //    }\n          //\n          // Note that we do not try to relocate f1()/f2() to be inside the namespace because other type\n          // signatures may reference them directly (without using the namespace qualifier).\n\n          writer.ensureSkippedLine();\n          writer.writeLine(`declare namespace ${entity.nameForEmit} {`);\n\n          // all local exports of local imported module are just references to top-level declarations\n          writer.increaseIndent();\n          writer.writeLine('export {');\n          writer.increaseIndent();\n\n          const exportClauses: string[] = [];\n          for (const [exportedName, exportedEntity] of astModuleExportInfo.exportedLocalEntities) {\n            const collectorEntity: CollectorEntity | undefined =\n              collector.tryGetCollectorEntity(exportedEntity);\n            if (collectorEntity === undefined) {\n              // This should never happen\n              // top-level exports of local imported module should be added as collector entities before\n              throw new InternalError(\n                `Cannot find collector entity for ${entity.nameForEmit}.${exportedEntity.localName}`\n              );\n            }\n\n            if (collectorEntity.nameForEmit === exportedName) {\n              exportClauses.push(collectorEntity.nameForEmit);\n            } else {\n              exportClauses.push(`${collectorEntity.nameForEmit} as ${exportedName}`);\n            }\n          }\n          writer.writeLine(exportClauses.join(',\\n'));\n\n          writer.decreaseIndent();\n          writer.writeLine('}'); // end of \"export { ... }\"\n          writer.decreaseIndent();\n          writer.writeLine('}'); // end of \"declare namespace { ... }\"\n        }\n\n        // Now emit the export statements for this entity.\n        for (const exportToEmit of exportsToEmit.values()) {\n          // Write any associated messages\n          if (exportToEmit.associatedMessages.length > 0) {\n            writer.ensureSkippedLine();\n            for (const message of exportToEmit.associatedMessages) {\n              ApiReportGenerator._writeLineAsComments(\n                writer,\n                'Warning: ' + message.formatMessageWithoutLocation()\n              );\n            }\n          }\n\n          DtsEmitHelpers.emitNamedExport(writer, exportToEmit.exportName, entity);\n        }\n        writer.ensureSkippedLine();\n      }\n    }\n\n    DtsEmitHelpers.emitStarExports(writer, collector);\n\n    // Write the unassociated warnings at the bottom of the file\n    const unassociatedMessages: ExtractorMessage[] =\n      collector.messageRouter.fetchUnassociatedMessagesForReviewFile();\n    if (unassociatedMessages.length > 0) {\n      writer.ensureSkippedLine();\n      ApiReportGenerator._writeLineAsComments(writer, 'Warnings were encountered during analysis:');\n      ApiReportGenerator._writeLineAsComments(writer, '');\n      for (const unassociatedMessage of unassociatedMessages) {\n        ApiReportGenerator._writeLineAsComments(\n          writer,\n          unassociatedMessage.formatMessageWithLocation(collector.workingPackage.packageFolder)\n        );\n      }\n    }\n\n    if (collector.workingPackage.tsdocComment === undefined) {\n      writer.ensureSkippedLine();\n      ApiReportGenerator._writeLineAsComments(writer, '(No @packageDocumentation comment for this package)');\n    }\n\n    // Write the closing delimiter for the Markdown code fence\n    writer.ensureSkippedLine();\n    writer.writeLine('```');\n\n    // Remove any trailing spaces\n    return writer.toString().replace(ApiReportGenerator._trimSpacesRegExp, '');\n  }\n\n  /**\n   * Before writing out a declaration, _modifySpan() applies various fixups to make it nice.\n   */\n  private static _modifySpan(\n    collector: Collector,\n    span: Span,\n    entity: CollectorEntity,\n    astDeclaration: AstDeclaration,\n    insideTypeLiteral: boolean,\n    reportVariant: ApiReportVariant\n  ): void {\n    // Should we process this declaration at all?\n    // eslint-disable-next-line no-bitwise\n    if (!ApiReportGenerator._shouldIncludeDeclaration(collector, astDeclaration, reportVariant)) {\n      span.modification.skipAll();\n      return;\n    }\n\n    const previousSpan: Span | undefined = span.previousSibling;\n\n    let recurseChildren: boolean = true;\n    let sortChildren: boolean = false;\n\n    switch (span.kind) {\n      case ts.SyntaxKind.JSDocComment:\n        span.modification.skipAll();\n        // For now, we don't transform JSDoc comment nodes at all\n        recurseChildren = false;\n        break;\n\n      case ts.SyntaxKind.ExportKeyword:\n      case ts.SyntaxKind.DefaultKeyword:\n      case ts.SyntaxKind.DeclareKeyword:\n        // Delete any explicit \"export\" or \"declare\" keywords -- we will re-add them below\n        span.modification.skipAll();\n        break;\n\n      case ts.SyntaxKind.InterfaceKeyword:\n      case ts.SyntaxKind.ClassKeyword:\n      case ts.SyntaxKind.EnumKeyword:\n      case ts.SyntaxKind.NamespaceKeyword:\n      case ts.SyntaxKind.ModuleKeyword:\n      case ts.SyntaxKind.TypeKeyword:\n      case ts.SyntaxKind.FunctionKeyword:\n        // Replace the stuff we possibly deleted above\n        let replacedModifiers: string = '';\n\n        if (entity.shouldInlineExport) {\n          replacedModifiers = 'export ' + replacedModifiers;\n        }\n\n        if (previousSpan && previousSpan.kind === ts.SyntaxKind.SyntaxList) {\n          // If there is a previous span of type SyntaxList, then apply it before any other modifiers\n          // (e.g. \"abstract\") that appear there.\n          previousSpan.modification.prefix = replacedModifiers + previousSpan.modification.prefix;\n        } else {\n          // Otherwise just stick it in front of this span\n          span.modification.prefix = replacedModifiers + span.modification.prefix;\n        }\n        break;\n\n      case ts.SyntaxKind.SyntaxList:\n        if (span.parent) {\n          if (AstDeclaration.isSupportedSyntaxKind(span.parent.kind)) {\n            // If the immediate parent is an API declaration, and the immediate children are API declarations,\n            // then sort the children alphabetically\n            sortChildren = true;\n          } else if (span.parent.kind === ts.SyntaxKind.ModuleBlock) {\n            // Namespaces are special because their chain goes ModuleDeclaration -> ModuleBlock -> SyntaxList\n            sortChildren = true;\n          }\n        }\n        break;\n\n      case ts.SyntaxKind.VariableDeclaration:\n        if (!span.parent) {\n          // The VariableDeclaration node is part of a VariableDeclarationList, however\n          // the Entry.followedSymbol points to the VariableDeclaration part because\n          // multiple definitions might share the same VariableDeclarationList.\n          //\n          // Since we are emitting a separate declaration for each one, we need to look upwards\n          // in the ts.Node tree and write a copy of the enclosing VariableDeclarationList\n          // content (e.g. \"var\" from \"var x=1, y=2\").\n          const list: ts.VariableDeclarationList | undefined = TypeScriptHelpers.matchAncestor(span.node, [\n            ts.SyntaxKind.VariableDeclarationList,\n            ts.SyntaxKind.VariableDeclaration\n          ]);\n          if (!list) {\n            // This should not happen unless the compiler API changes somehow\n            throw new InternalError('Unsupported variable declaration');\n          }\n          const listPrefix: string = list\n            .getSourceFile()\n            .text.substring(list.getStart(), list.declarations[0].getStart());\n          span.modification.prefix = listPrefix + span.modification.prefix;\n          span.modification.suffix = ';';\n\n          if (entity.shouldInlineExport) {\n            span.modification.prefix = 'export ' + span.modification.prefix;\n          }\n        }\n        break;\n\n      case ts.SyntaxKind.Identifier:\n        const referencedEntity: CollectorEntity | undefined = collector.tryGetEntityForNode(\n          span.node as ts.Identifier\n        );\n\n        if (referencedEntity) {\n          if (!referencedEntity.nameForEmit) {\n            // This should never happen\n            throw new InternalError('referencedEntry.nameForEmit is undefined');\n          }\n\n          span.modification.prefix = referencedEntity.nameForEmit;\n          // For debugging:\n          // span.modification.prefix += '/*R=FIX*/';\n        } else {\n          // For debugging:\n          // span.modification.prefix += '/*R=KEEP*/';\n        }\n\n        break;\n\n      case ts.SyntaxKind.TypeLiteral:\n        insideTypeLiteral = true;\n        break;\n\n      case ts.SyntaxKind.ImportType:\n        DtsEmitHelpers.modifyImportTypeSpan(\n          collector,\n          span,\n          astDeclaration,\n          (childSpan, childAstDeclaration) => {\n            ApiReportGenerator._modifySpan(\n              collector,\n              childSpan,\n              entity,\n              childAstDeclaration,\n              insideTypeLiteral,\n              reportVariant\n            );\n          }\n        );\n        break;\n    }\n\n    if (recurseChildren) {\n      for (const child of span.children) {\n        let childAstDeclaration: AstDeclaration = astDeclaration;\n\n        if (AstDeclaration.isSupportedSyntaxKind(child.kind)) {\n          childAstDeclaration = collector.astSymbolTable.getChildAstDeclarationByNode(\n            child.node,\n            astDeclaration\n          );\n\n          if (ApiReportGenerator._shouldIncludeDeclaration(collector, childAstDeclaration, reportVariant)) {\n            if (sortChildren) {\n              span.modification.sortChildren = true;\n              child.modification.sortKey = Collector.getSortKeyIgnoringUnderscore(\n                childAstDeclaration.astSymbol.localName\n              );\n            }\n\n            if (!insideTypeLiteral) {\n              const messagesToReport: ExtractorMessage[] =\n                collector.messageRouter.fetchAssociatedMessagesForReviewFile(childAstDeclaration);\n\n              // NOTE: This generates ae-undocumented messages as a side effect\n              const aedocSynopsis: string = ApiReportGenerator._getAedocSynopsis(\n                collector,\n                childAstDeclaration,\n                messagesToReport\n              );\n\n              child.modification.prefix = aedocSynopsis + child.modification.prefix;\n            }\n          }\n        }\n\n        ApiReportGenerator._modifySpan(\n          collector,\n          child,\n          entity,\n          childAstDeclaration,\n          insideTypeLiteral,\n          reportVariant\n        );\n      }\n    }\n  }\n\n  private static _shouldIncludeDeclaration(\n    collector: Collector,\n    astDeclaration: AstDeclaration,\n    reportVariant: ApiReportVariant\n  ): boolean {\n    // Private declarations are not included in the API report\n    // eslint-disable-next-line no-bitwise\n    if ((astDeclaration.modifierFlags & ts.ModifierFlags.Private) !== 0) {\n      return false;\n    }\n\n    const apiItemMetadata: ApiItemMetadata = collector.fetchApiItemMetadata(astDeclaration);\n\n    return this._shouldIncludeReleaseTag(apiItemMetadata.effectiveReleaseTag, reportVariant);\n  }\n\n  private static _shouldIncludeReleaseTag(releaseTag: ReleaseTag, reportVariant: ApiReportVariant): boolean {\n    switch (reportVariant) {\n      case 'complete':\n        return true;\n      case 'alpha':\n        return (\n          releaseTag === ReleaseTag.Alpha ||\n          releaseTag === ReleaseTag.Beta ||\n          releaseTag === ReleaseTag.Public ||\n          // NOTE: No specified release tag is implicitly treated as `@public`.\n          releaseTag === ReleaseTag.None\n        );\n      case 'beta':\n        return (\n          releaseTag === ReleaseTag.Beta ||\n          releaseTag === ReleaseTag.Public ||\n          // NOTE: No specified release tag is implicitly treated as `@public`.\n          releaseTag === ReleaseTag.None\n        );\n      case 'public':\n        return (\n          releaseTag === ReleaseTag.Public ||\n          // NOTE: No specified release tag is implicitly treated as `@public`.\n          releaseTag === ReleaseTag.None\n        );\n      default:\n        throw new Error(`Unrecognized release level: ${reportVariant}`);\n    }\n  }\n\n  /**\n   * For declarations marked as `@preapproved`, this is used instead of _modifySpan().\n   */\n  private static _modifySpanForPreapproved(span: Span): void {\n    // Match something like this:\n    //\n    //   ClassDeclaration:\n    //     SyntaxList:\n    //       ExportKeyword:  pre=[export] sep=[ ]\n    //       DeclareKeyword:  pre=[declare] sep=[ ]\n    //     ClassKeyword:  pre=[class] sep=[ ]\n    //     Identifier:  pre=[_PreapprovedClass] sep=[ ]\n    //     FirstPunctuation:  pre=[{] sep=[\\n\\n    ]\n    //     SyntaxList:\n    //       ...\n    //     CloseBraceToken:  pre=[}]\n    //\n    // or this:\n    //   ModuleDeclaration:\n    //     SyntaxList:\n    //       ExportKeyword:  pre=[export] sep=[ ]\n    //       DeclareKeyword:  pre=[declare] sep=[ ]\n    //     NamespaceKeyword:  pre=[namespace] sep=[ ]\n    //     Identifier:  pre=[_PreapprovedNamespace] sep=[ ]\n    //     ModuleBlock:\n    //       FirstPunctuation:  pre=[{] sep=[\\n\\n    ]\n    //       SyntaxList:\n    //         ...\n    //       CloseBraceToken:  pre=[}]\n    //\n    // And reduce it to something like this:\n    //\n    //   // @internal (undocumented)\n    //   class _PreapprovedClass { /* (preapproved) */ }\n    //\n\n    let skipRest: boolean = false;\n    for (const child of span.children) {\n      if (skipRest || child.kind === ts.SyntaxKind.SyntaxList || child.kind === ts.SyntaxKind.JSDocComment) {\n        child.modification.skipAll();\n      }\n      if (child.kind === ts.SyntaxKind.Identifier) {\n        skipRest = true;\n        child.modification.omitSeparatorAfter = true;\n        child.modification.suffix = ' { /* (preapproved) */ }';\n      }\n    }\n  }\n\n  /**\n   * Writes a synopsis of the AEDoc comments, which indicates the release tag,\n   * whether the item has been documented, and any warnings that were detected\n   * by the analysis.\n   */\n  private static _getAedocSynopsis(\n    collector: Collector,\n    astDeclaration: AstDeclaration,\n    messagesToReport: ExtractorMessage[]\n  ): string {\n    const writer: IndentedWriter = new IndentedWriter();\n\n    for (const message of messagesToReport) {\n      ApiReportGenerator._writeLineAsComments(writer, 'Warning: ' + message.formatMessageWithoutLocation());\n    }\n\n    if (!collector.isAncillaryDeclaration(astDeclaration)) {\n      const footerParts: string[] = [];\n      const apiItemMetadata: ApiItemMetadata = collector.fetchApiItemMetadata(astDeclaration);\n\n      // 1. Release tag (if present)\n      if (!apiItemMetadata.releaseTagSameAsParent) {\n        if (apiItemMetadata.effectiveReleaseTag !== ReleaseTag.None) {\n          footerParts.push(ReleaseTag.getTagName(apiItemMetadata.effectiveReleaseTag));\n        }\n      }\n\n      // 2. Enumerate configured tags, reporting standard system tags first and then other configured tags.\n      // Note that the ordering we handle the standard tags is important for backwards compatibility.\n      // Also note that we had special mechanisms for checking whether or not an item is documented with these tags,\n      // so they are checked specially.\n      const {\n        '@sealed': reportSealedTag,\n        '@virtual': reportVirtualTag,\n        '@override': reportOverrideTag,\n        '@eventProperty': reportEventPropertyTag,\n        '@deprecated': reportDeprecatedTag,\n        ...otherTagsToReport\n      } = collector.extractorConfig.tagsToReport;\n\n      // 2.a Check for standard tags and report those that are both configured and present in the metadata.\n      if (reportSealedTag && apiItemMetadata.isSealed) {\n        footerParts.push('@sealed');\n      }\n      if (reportVirtualTag && apiItemMetadata.isVirtual) {\n        footerParts.push('@virtual');\n      }\n      if (reportOverrideTag && apiItemMetadata.isOverride) {\n        footerParts.push('@override');\n      }\n      if (reportEventPropertyTag && apiItemMetadata.isEventProperty) {\n        footerParts.push('@eventProperty');\n      }\n      if (reportDeprecatedTag && apiItemMetadata.tsdocComment?.deprecatedBlock) {\n        footerParts.push('@deprecated');\n      }\n\n      // 2.b Check for other configured tags and report those that are present in the tsdoc metadata.\n      for (const [tag, reportTag] of Object.entries(otherTagsToReport)) {\n        if (reportTag) {\n          // If the tag was not handled specially, check if it is present in the metadata.\n          if (apiItemMetadata.tsdocComment?.customBlocks.some((block) => block.blockTag.tagName === tag)) {\n            footerParts.push(tag);\n          } else if (apiItemMetadata.tsdocComment?.modifierTagSet.hasTagName(tag)) {\n            footerParts.push(tag);\n          }\n        }\n      }\n\n      // 3. If the item is undocumented, append notice at the end of the list\n      if (apiItemMetadata.undocumented) {\n        footerParts.push('(undocumented)');\n\n        collector.messageRouter.addAnalyzerIssue(\n          ExtractorMessageId.Undocumented,\n          `Missing documentation for \"${astDeclaration.astSymbol.localName}\".`,\n          astDeclaration\n        );\n      }\n\n      if (footerParts.length > 0) {\n        if (messagesToReport.length > 0) {\n          ApiReportGenerator._writeLineAsComments(writer, ''); // skip a line after the warnings\n        }\n\n        ApiReportGenerator._writeLineAsComments(writer, footerParts.join(' '));\n      }\n    }\n\n    return writer.toString();\n  }\n\n  private static _writeLineAsComments(writer: IndentedWriter, line: string): void {\n    const lines: string[] = Text.convertToLf(line).split('\\n');\n    for (const realLine of lines) {\n      writer.write('// ');\n      writer.write(realLine);\n      writer.writeLine();\n    }\n  }\n}\n"]}