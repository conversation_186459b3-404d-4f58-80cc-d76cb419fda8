# @ast-grep/napi

<p align=center>
  <img src="https://ast-grep.github.io/logo.svg" alt="ast-grep"/>
</p>

## ast-grep(sg)

ast-grep(sg) is a CLI tool for code structural search, lint, and rewriting.

This npm package is for programmatic usage of ast-grep.
Please see the [API usage guide](https://ast-grep.github.io/guide/api-usage.html) and [API reference](https://ast-grep.github.io/reference/api.html).

Other resources include [ast-grep's official site](https://ast-grep.github.io/) and [repository](https://github.com/ast-grep/ast-grep).

## Support matrix

### Operating Systems

|                  | node14 | node16 | node18 |
| ---------------- | ------ | ------ | ------ |
| Windows x64      | ✓      | ✓      | ✓      |
| macOS x64        | ✓      | ✓      | ✓      |
| macOS arm64      | ✓      | ✓      | ✓      |
| Linux x64 gnu    | ✓      | ✓      | ✓      |
| Windows x32      | ✓      | ✓      | ✓      |
| Windows arm64    | ✓      | ✓      | ✓      |
<!-- | Linux arm gnu    | ✓      | ✓      | ✓      | -->
<!-- | Linux x64 musl   | ✓      | ✓      | ✓      | -->
<!-- | Linux arm64 gnu  | ✓      | ✓      | ✓      | -->
<!-- | Linux arm64 musl | ✓      | ✓      | ✓      | -->
<!-- | Android arm64    | ✓      | ✓      | ✓      | -->
<!-- | Android armv7    | ✓      | ✓      | ✓      | -->
<!-- | FreeBSD x64      | ✓      | ✓      | ✓      | -->
