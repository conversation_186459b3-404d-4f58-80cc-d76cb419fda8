// Auto-generated from tree-sitter Css v0.23.2
type CssTypes = {
  "adjacent_sibling_selector": {
    "type": "adjacent_sibling_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "arguments": {
    "type": "arguments",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "at_rule": {
    "type": "at_rule",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "at_keyword",
          "named": true
        },
        {
          "type": "binary_query",
          "named": true
        },
        {
          "type": "block",
          "named": true
        },
        {
          "type": "feature_query",
          "named": true
        },
        {
          "type": "keyword_query",
          "named": true
        },
        {
          "type": "parenthesized_query",
          "named": true
        },
        {
          "type": "selector_query",
          "named": true
        },
        {
          "type": "unary_query",
          "named": true
        }
      ]
    }
  },
  "attribute_name": {
    "type": "attribute_name",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "attribute_selector": {
    "type": "attribute_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_name",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "binary_expression": {
    "type": "binary_expression",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        }
      ]
    }
  },
  "binary_query": {
    "type": "binary_query",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "binary_query",
          "named": true
        },
        {
          "type": "feature_query",
          "named": true
        },
        {
          "type": "keyword_query",
          "named": true
        },
        {
          "type": "parenthesized_query",
          "named": true
        },
        {
          "type": "selector_query",
          "named": true
        },
        {
          "type": "unary_query",
          "named": true
        }
      ]
    }
  },
  "block": {
    "type": "block",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "at_rule",
          "named": true
        },
        {
          "type": "charset_statement",
          "named": true
        },
        {
          "type": "declaration",
          "named": true
        },
        {
          "type": "import_statement",
          "named": true
        },
        {
          "type": "keyframes_statement",
          "named": true
        },
        {
          "type": "media_statement",
          "named": true
        },
        {
          "type": "namespace_statement",
          "named": true
        },
        {
          "type": "postcss_statement",
          "named": true
        },
        {
          "type": "rule_set",
          "named": true
        },
        {
          "type": "supports_statement",
          "named": true
        }
      ]
    }
  },
  "call_expression": {
    "type": "call_expression",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "arguments",
          "named": true
        },
        {
          "type": "function_name",
          "named": true
        }
      ]
    }
  },
  "charset_statement": {
    "type": "charset_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        }
      ]
    }
  },
  "child_selector": {
    "type": "child_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "class_name": {
    "type": "class_name",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "escape_sequence",
          "named": true
        },
        {
          "type": "identifier",
          "named": true
        }
      ]
    }
  },
  "class_selector": {
    "type": "class_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_name",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "color_value": {
    "type": "color_value",
    "named": true,
    "fields": {}
  },
  "declaration": {
    "type": "declaration",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "property_name",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        }
      ]
    }
  },
  "descendant_selector": {
    "type": "descendant_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "feature_query": {
    "type": "feature_query",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "feature_name",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        }
      ]
    }
  },
  "float_value": {
    "type": "float_value",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "unit",
          "named": true
        }
      ]
    }
  },
  "grid_value": {
    "type": "grid_value",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        }
      ]
    }
  },
  "id_selector": {
    "type": "id_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_name",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "import_statement": {
    "type": "import_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "binary_query",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "feature_query",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "keyword_query",
          "named": true
        },
        {
          "type": "parenthesized_query",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "selector_query",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "unary_query",
          "named": true
        }
      ]
    }
  },
  "integer_value": {
    "type": "integer_value",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": false,
      "types": [
        {
          "type": "unit",
          "named": true
        }
      ]
    }
  },
  "keyframe_block": {
    "type": "keyframe_block",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "block",
          "named": true
        },
        {
          "type": "from",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "to",
          "named": true
        }
      ]
    }
  },
  "keyframe_block_list": {
    "type": "keyframe_block_list",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "keyframe_block",
          "named": true
        }
      ]
    }
  },
  "keyframes_statement": {
    "type": "keyframes_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "at_keyword",
          "named": true
        },
        {
          "type": "keyframe_block_list",
          "named": true
        },
        {
          "type": "keyframes_name",
          "named": true
        }
      ]
    }
  },
  "media_statement": {
    "type": "media_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "binary_query",
          "named": true
        },
        {
          "type": "block",
          "named": true
        },
        {
          "type": "feature_query",
          "named": true
        },
        {
          "type": "keyword_query",
          "named": true
        },
        {
          "type": "parenthesized_query",
          "named": true
        },
        {
          "type": "selector_query",
          "named": true
        },
        {
          "type": "unary_query",
          "named": true
        }
      ]
    }
  },
  "namespace_selector": {
    "type": "namespace_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "namespace_statement": {
    "type": "namespace_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "namespace_name",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        }
      ]
    }
  },
  "parenthesized_query": {
    "type": "parenthesized_query",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "binary_query",
          "named": true
        },
        {
          "type": "feature_query",
          "named": true
        },
        {
          "type": "keyword_query",
          "named": true
        },
        {
          "type": "parenthesized_query",
          "named": true
        },
        {
          "type": "selector_query",
          "named": true
        },
        {
          "type": "unary_query",
          "named": true
        }
      ]
    }
  },
  "parenthesized_value": {
    "type": "parenthesized_value",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        }
      ]
    }
  },
  "postcss_statement": {
    "type": "postcss_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "at_keyword",
          "named": true
        },
        {
          "type": "binary_expression",
          "named": true
        },
        {
          "type": "call_expression",
          "named": true
        },
        {
          "type": "color_value",
          "named": true
        },
        {
          "type": "float_value",
          "named": true
        },
        {
          "type": "grid_value",
          "named": true
        },
        {
          "type": "important",
          "named": true
        },
        {
          "type": "integer_value",
          "named": true
        },
        {
          "type": "parenthesized_value",
          "named": true
        },
        {
          "type": "plain_value",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        }
      ]
    }
  },
  "pseudo_class_selector": {
    "type": "pseudo_class_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "arguments",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_name",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "pseudo_element_selector": {
    "type": "pseudo_element_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "arguments",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "rule_set": {
    "type": "rule_set",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "block",
          "named": true
        },
        {
          "type": "selectors",
          "named": true
        }
      ]
    }
  },
  "selector_query": {
    "type": "selector_query",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "selectors": {
    "type": "selectors",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "sibling_selector": {
    "type": "sibling_selector",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "adjacent_sibling_selector",
          "named": true
        },
        {
          "type": "attribute_selector",
          "named": true
        },
        {
          "type": "child_selector",
          "named": true
        },
        {
          "type": "class_selector",
          "named": true
        },
        {
          "type": "descendant_selector",
          "named": true
        },
        {
          "type": "id_selector",
          "named": true
        },
        {
          "type": "namespace_selector",
          "named": true
        },
        {
          "type": "nesting_selector",
          "named": true
        },
        {
          "type": "pseudo_class_selector",
          "named": true
        },
        {
          "type": "pseudo_element_selector",
          "named": true
        },
        {
          "type": "sibling_selector",
          "named": true
        },
        {
          "type": "string_value",
          "named": true
        },
        {
          "type": "tag_name",
          "named": true
        },
        {
          "type": "universal_selector",
          "named": true
        }
      ]
    }
  },
  "string_value": {
    "type": "string_value",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "escape_sequence",
          "named": true
        },
        {
          "type": "string_content",
          "named": true
        }
      ]
    }
  },
  "stylesheet": {
    "type": "stylesheet",
    "named": true,
    "root": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": false,
      "types": [
        {
          "type": "at_rule",
          "named": true
        },
        {
          "type": "charset_statement",
          "named": true
        },
        {
          "type": "declaration",
          "named": true
        },
        {
          "type": "import_statement",
          "named": true
        },
        {
          "type": "keyframes_statement",
          "named": true
        },
        {
          "type": "media_statement",
          "named": true
        },
        {
          "type": "namespace_statement",
          "named": true
        },
        {
          "type": "rule_set",
          "named": true
        },
        {
          "type": "supports_statement",
          "named": true
        }
      ]
    }
  },
  "supports_statement": {
    "type": "supports_statement",
    "named": true,
    "fields": {},
    "children": {
      "multiple": true,
      "required": true,
      "types": [
        {
          "type": "binary_query",
          "named": true
        },
        {
          "type": "block",
          "named": true
        },
        {
          "type": "feature_query",
          "named": true
        },
        {
          "type": "keyword_query",
          "named": true
        },
        {
          "type": "parenthesized_query",
          "named": true
        },
        {
          "type": "selector_query",
          "named": true
        },
        {
          "type": "unary_query",
          "named": true
        }
      ]
    }
  },
  "unary_query": {
    "type": "unary_query",
    "named": true,
    "fields": {},
    "children": {
      "multiple": false,
      "required": true,
      "types": [
        {
          "type": "binary_query",
          "named": true
        },
        {
          "type": "feature_query",
          "named": true
        },
        {
          "type": "keyword_query",
          "named": true
        },
        {
          "type": "parenthesized_query",
          "named": true
        },
        {
          "type": "selector_query",
          "named": true
        },
        {
          "type": "unary_query",
          "named": true
        }
      ]
    }
  },
  "universal_selector": {
    "type": "universal_selector",
    "named": true,
    "fields": {}
  },
  "at_keyword": {
    "type": "at_keyword",
    "named": true
  },
  "comment": {
    "type": "comment",
    "named": true
  },
  "escape_sequence": {
    "type": "escape_sequence",
    "named": true
  },
  "feature_name": {
    "type": "feature_name",
    "named": true
  },
  "from": {
    "type": "from",
    "named": true
  },
  "function_name": {
    "type": "function_name",
    "named": true
  },
  "id_name": {
    "type": "id_name",
    "named": true
  },
  "identifier": {
    "type": "identifier",
    "named": true
  },
  "important": {
    "type": "important",
    "named": true
  },
  "js_comment": {
    "type": "js_comment",
    "named": true
  },
  "keyframes_name": {
    "type": "keyframes_name",
    "named": true
  },
  "keyword_query": {
    "type": "keyword_query",
    "named": true
  },
  "namespace_name": {
    "type": "namespace_name",
    "named": true
  },
  "nesting_selector": {
    "type": "nesting_selector",
    "named": true
  },
  "plain_value": {
    "type": "plain_value",
    "named": true
  },
  "property_name": {
    "type": "property_name",
    "named": true
  },
  "string_content": {
    "type": "string_content",
    "named": true
  },
  "tag_name": {
    "type": "tag_name",
    "named": true
  },
  "to": {
    "type": "to",
    "named": true
  },
  "unit": {
    "type": "unit",
    "named": true
  }
};
export default CssTypes;