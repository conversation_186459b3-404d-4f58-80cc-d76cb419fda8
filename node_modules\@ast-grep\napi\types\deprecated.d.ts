import type { SgRoot, SgNode } from './sgnode'
import type { NapiConfig, FindConfig } from './config'

/**
 * @deprecated language specific objects are deprecated
 * use the equivalent functions like `parse` in @ast-grep/napi
 */
export declare namespace html {
  /** @deprecated use `parse(Lang.Html, src)` instead */
  export function parse(src: string): SgRoot
  /** @deprecated use `parseAsync(Lang.Html, src)` instead */
  export function parseAsync(src: string): Promise<SgRoot>
  /** @deprecated use `kind(Lang.Html, kindName)` instead */
  export function kind(kindName: string): number
  /** @deprecated use `pattern(Lang.Html, p)` instead */
  export function pattern(pattern: string): NapiConfig
  /** @deprecated use `findInFiles(Lang.Html, config, callback)` instead */
  export function findInFiles(
    config: FindConfig,
    callback: (err: null | Error, result: SgNode[]) => void,
  ): Promise<number>
}
/**
 * @deprecated language specific objects are deprecated
 * use the equivalent functions like `parse` in @ast-grep/napi
 */
export declare namespace js {
  /** @deprecated use `parse(Lang.JavaScript, src)` instead */
  export function parse(src: string): SgRoot
  /** @deprecated use `parseAsync(Lang.JavaScript, src)` instead */
  export function parseAsync(src: string): Promise<SgRoot>
  /** @deprecated use `kind(Lang.JavaScript, kindName)` instead */
  export function kind(kindName: string): number
  /** @deprecated use `pattern(Lang.JavaScript, p)` instead */
  export function pattern(pattern: string): NapiConfig
  /** @deprecated use `findInFiles(Lang.JavaScript, config, callback)` instead */
  export function findInFiles(
    config: FindConfig,
    callback: (err: null | Error, result: SgNode[]) => void,
  ): Promise<number>
}
/**
 * @deprecated language specific objects are deprecated
 * use the equivalent functions like `parse` in @ast-grep/napi
 */
export declare namespace jsx {
  /** @deprecated use `parse(Lang.JavaScript, src)` instead */
  export function parse(src: string): SgRoot
  /** @deprecated use `parseAsync(Lang.JavaScript, src)` instead */
  export function parseAsync(src: string): Promise<SgRoot>
  /** @deprecated use `kind(Lang.JavaScript, kindName)` instead */
  export function kind(kindName: string): number
  /** @deprecated use `pattern(Lang.JavaScript, p)` instead */
  export function pattern(pattern: string): NapiConfig
  /** @deprecated use `findInFiles(Lang.JavaScript, config, callback)` instead */
  export function findInFiles(
    config: FindConfig,
    callback: (err: null | Error, result: SgNode[]) => void,
  ): Promise<number>
}
/**
 * @deprecated language specific objects are deprecated
 * use the equivalent functions like `parse` in @ast-grep/napi
 */
export declare namespace ts {
  /** @deprecated use `parse(Lang.TypeScript, src)` instead */
  export function parse(src: string): SgRoot
  /** @deprecated use `parseAsync(Lang.TypeScript, src)` instead */
  export function parseAsync(src: string): Promise<SgRoot>
  /** @deprecated use `kind(Lang.TypeScript, kindName)` instead */
  export function kind(kindName: string): number
  /** @deprecated use `pattern(Lang.TypeScript, p)` instead */
  export function pattern(pattern: string): NapiConfig
  /** @deprecated use `findInFiles(Lang.TypeScript, config, callback)` instead */
  export function findInFiles(
    config: FindConfig,
    callback: (err: null | Error, result: SgNode[]) => void,
  ): Promise<number>
}
/**
 * @deprecated language specific objects are deprecated
 * use the equivalent functions like `parse` in @ast-grep/napi
 */
export declare namespace tsx {
  /** @deprecated use `parse(Lang.Tsx, src)` instead */
  export function parse(src: string): SgRoot
  /** @deprecated use `parseAsync(Lang.Tsx, src)` instead */
  export function parseAsync(src: string): Promise<SgRoot>
  /** @deprecated use `kind(Lang.Tsx, kindName)` instead */
  export function kind(kindName: string): number
  /** @deprecated use `pattern(Lang.Tsx, p)` instead */
  export function pattern(pattern: string): NapiConfig
  /** @deprecated use `findInFiles(Lang.Tsx, config, callback)` instead */
  export function findInFiles(
    config: FindConfig,
    callback: (err: null | Error, result: SgNode[]) => void,
  ): Promise<number>
}
/**
 * @deprecated language specific objects are deprecated
 * use the equivalent functions like `parse` in @ast-grep/napi
 */
export declare namespace css {
  /** @deprecated use `parse(Lang.Css, src)` instead */
  export function parse(src: string): SgRoot
  /** @deprecated use `parseAsync(Lang.Css, src)` instead */
  export function parseAsync(src: string): Promise<SgRoot>
  /** @deprecated use `kind(Lang.Css, kindName)` instead */
  export function kind(kindName: string): number
  /** @deprecated use `pattern(Lang.Css, p)` instead */
  export function pattern(pattern: string): NapiConfig
  /** @deprecated use `findInFiles(Lang.Css, config, callback)` instead */
  export function findInFiles(
    config: FindConfig,
    callback: (err: null | Error, result: SgNode[]) => void,
  ): Promise<number>
}
